/**
 * @description 错误边界模块导出
 */

import React from 'react';

// 主要组件
export {default as ErrorBoundary} from './ErrorBoundary';
import ErrorBoundary from './ErrorBoundary';
export {
  GlobalErrorFallback,
  PageErrorFallback,
  ComponentErrorFallback,
} from './ErrorFallback';

// 高阶组件
export {
  default as withErrorBoundary,
  withPageErrorBoundary,
  withComponentErrorBoundary,
  withGlobalErrorBoundary,
} from './withErrorBoundary';

// Hooks
export {
  useErrorHandler,
  useAsyncErrorHandler,
  useGlobalErrorHandler,
  useUnhandledPromiseRejectionHandler,
  useErrorRetry,
  useErrorBoundaryState,
} from './hooks';

// 服务
export {default as errorReportService} from './ErrorReportService';

// 配置
export {
  default as getErrorBoundaryConfig,
  defaultErrorBoundaryConfig,
} from './config';

// 类型
export type {
  ErrorInfo,
  ErrorDetails,
  ErrorBoundaryState,
  ErrorBoundaryProps,
  ErrorFallbackProps,
  ErrorReportService,
  ErrorRecoveryOptions,
  ErrorBoundaryLevel,
  ErrorHandlerHookReturn,
  AsyncErrorHandlerHookReturn,
  ErrorRetryHookReturn,
  ErrorBoundaryConfig,
  DeviceInfo,
  ErrorQueueItem,
  ErrorReportPayload,
} from './types';

// 便捷函数
export const createErrorBoundary = (
  props: Partial<import('./types').ErrorBoundaryProps> = {},
): React.FC<{children: React.ReactNode}> => {
  return function ErrorBoundaryWrapper({
    children,
  }: {
    children: React.ReactNode;
  }) {
    return <ErrorBoundary {...props}>{children}</ErrorBoundary>;
  };
};

// 预设配置
export const ErrorBoundaryPresets = {
  // 全局应用级别
  Global: createErrorBoundary({
    level: 'global',
    name: 'App',
    enableRetry: true,
    maxRetries: 1,
  }),

  // 页面级别
  Page: (pageName: string) =>
    createErrorBoundary({
      level: 'page',
      name: pageName,
      enableRetry: true,
      maxRetries: 2,
    }),

  // 组件级别
  Component: (componentName: string) =>
    createErrorBoundary({
      level: 'component',
      name: componentName,
      enableRetry: true,
      maxRetries: 1,
      isolate: true,
    }),

  // 严格模式（不允许重试）
  Strict: createErrorBoundary({
    enableRetry: false,
    maxRetries: 0,
  }),

  // 开发模式（显示详细错误信息）
  Development: createErrorBoundary({
    enableRetry: true,
    maxRetries: 5,
    onError: (error, errorInfo) => {
      if (__DEV__) {
        // eslint-disable-next-line no-console
        console.group('🚨 Error Boundary Triggered');

        console.error('Error:', error);

        console.error('Error Info:', errorInfo);

        // eslint-disable-next-line no-console
        console.groupEnd();
      }
    },
  }),
};
