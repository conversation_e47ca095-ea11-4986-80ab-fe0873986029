import React from 'react';
import {PixelRatio, Pressable, StyleSheet, Text, View} from 'react-native';

export default function RadioButton({
  borderColor,
  color = '#77BC1F',
  inacticeColor = '#979797',
  containerStyle,
  disabled = false,
  id,
  label,
  labelStyle,
  layout = 'row',
  onPress,
  selected = false,
  size = 24,
  innerSize = 6,
  activeBorderSize = 7.5,
  inactiveBorderSize = 2,
}) {
  // const borderWidth = PixelRatio.roundToNearestPixel(borderSize);
  const sizeHalf = PixelRatio.roundToNearestPixel(size * 0.5);
  const sizeFull = PixelRatio.roundToNearestPixel(size);

  let orientation = {flexDirection: 'row'};
  let margin = {marginLeft: 10};

  if (layout === 'column') {
    orientation = {alignItems: 'center'};
    margin = {marginTop: 10};
  }

  function handlePress() {
    if (disabled) {
      return null;
    }
    if (onPress) {
      onPress(id);
    }
  }

  return (
    <>
      <Pressable
        onPress={handlePress}
        style={[
          styles.container,
          orientation,
          {opacity: disabled ? 0.2 : 1},
          containerStyle,
        ]}>
        {selected ? (
          <View
            style={[
              styles.border,
              {
                borderColor: borderColor || color,
                borderWidth: activeBorderSize,
                width: sizeFull,
                height: sizeFull,
                borderRadius: sizeHalf,
              },
            ]}>
            <View
              style={{
                backgroundColor: '#fff',
                width: innerSize,
                height: innerSize,
                borderRadius: innerSize,
              }}
            />
          </View>
        ) : (
          <View
            style={[
              styles.border,
              {
                borderColor: inacticeColor || color,
                borderWidth: inactiveBorderSize,
                width: sizeFull,
                height: sizeFull,
                borderRadius: sizeHalf,
              },
            ]}
          />
        )}
        {Boolean(label) && <Text style={[margin, labelStyle]}>{label}</Text>}
      </Pressable>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    marginHorizontal: 10,
    marginVertical: 5,
  },
  border: {
    justifyContent: 'center',
    alignItems: 'center',
  },
});
