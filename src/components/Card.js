import React, {isValidElement} from 'react';
import {Text, View} from 'react-native';
import {CVNIcon} from '@cvn/rn-panel-kit';
import CustomStyleSheet from '@utils/style/index.js';

/**
 * 不直接在外层View加 opacity 不透明度，
 * 在子元素上加不透明度，同 SliderView
 * 增加 addOpacityToChildrenWhenDisable 参数，
 * 用于控制是否给子元素加不透明度，当需要禁用的时候
 */
export default ({
  children,
  title,
  icon,
  subTitle,
  style = {},
  disabled = false,
  rightElement,
  subElement,
  addOpacityToChildrenWhenDisable = true,
}) => {
  return (
    <View style={[styles.container, style, disabled && styles.disabled]}>
      <View style={styles.topHeader}>
        <View style={styles.headerContainer}>
          {icon &&
            (isValidElement(icon) ? (
              icon
            ) : (
              <CVNIcon style={styles.leftIcon} source={icon} />
            ))}
          <View style={styles.header}>
            <Text style={[styles.leftView, styles.title]}>
              {title}
              {subTitle ? (
                <Text style={styles.sub}>
                  <Text style={styles.sp}> | </Text>
                  {subTitle}
                </Text>
              ) : null}
            </Text>
            <View style={styles.rightView}>{rightElement}</View>
          </View>
        </View>
        {subElement}
      </View>
      <View
        style={
          disabled && addOpacityToChildrenWhenDisable ? styles.disabled : {}
        }>
        {children}
      </View>
    </View>
  );
};

const styles = CustomStyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    // boxShadow: '0px 0px 15px 0px rgba(0,0,0,0.05)',
    shadowOffset: {width: 0, height: 3},
    shadowRadius: 7.5,
    shadowOpacity: 0.05,
    // elevation: 3,
    paddingHorizontal: 10,
    paddingVertical: 16,
    marginHorizontal: 14,
    marginTop: 1,
    marginBottom: 13,
    borderRadius: 10,
  },
  topHeader: {
    marginBottom: 20,
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  header: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  leftView: {
    flexGrow: 0,
    flexShrink: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  rightView: {
    flexGrow: 1,
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  leftIcon: {
    width: 34,
    height: 34,
    marginRight: 10,
  },
  title: {
    color: '#000000',
    fontSize: 17,
    lineHeight: 20,
    fontWeight: '500',
  },
  sp: {
    paddingHorizontal: 12,
    color: '#000000',
    opacity: 0.5,
    fontSize: 15,
  },
  sub: {
    color: '#000000',
    opacity: 0.7,
    fontSize: 12,
  },
  disabled: {
    opacity: 0.5,
  },
});
