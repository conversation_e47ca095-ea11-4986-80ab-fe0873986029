/*
 * @Author: 邢立伟 <EMAIL>
 * @Date: 2023-09-27 10:14:59
 * @LastEditors: 邢立伟 <EMAIL>
 * @LastEditTime: 2024-07-17 14:55:53
 * @FilePath: /61004/src/pages/panel/components/BaseChart
 * @Description: 折线图封装
 */
import {View, Text, StyleSheet, ViewStyle} from 'react-native';
import React, {useEffect, useMemo, useRef} from 'react';
import {observer} from 'mobx-react';
import {ECharts} from '@cvn/react-native-echarts-wrapper';
import {NoContent} from '@components';
import {ChartDataProps} from '../mobx/utils/interfaceProps';
/**
 * 基础折线图组件
 * @param {Object} store - 注入的store对象
 * @param {Object} style - 自定义样式
 * @param {Object} homeChartData - 主页图表数据
 * @param {Object} chartData - 图表数据
 * @param {string} type - 类型
 * @returns {Node} - BaseLineChart组件
 * @example labels: ['07:00', '07:10', '07:20', '07:30', '07:40', '07:50', '08:00']
 * data: [0.016, 0.008, 0.009, 0.013, 0.005, 0.011, 0.022]
 */

interface InjectedStores {
  style: ViewStyle; // 自定义样式
  type: string; // 类型
  homeChartData?: ChartDataProps; // 主页图表数据
  chartData?: ChartDataProps; // 图表页数据
  selectedUnit?: string; // 单位
}

interface EChartsRef {
  setOption: (option: object) => void;
}
export const BaseLineChart = observer(
  ({
    homeChartData,
    chartData,
    style = {},
    type = '',
    selectedUnit,
  }: InjectedStores) => {
    let tmpChartData;
    let unit;
    // 一个字宽粗略估计是7.5
    const echartsDom = useRef<EChartsRef>(null);
    if (type === 'home') {
      tmpChartData = homeChartData;
      unit = homeChartData?.unit;
    } else {
      tmpChartData = chartData;
      unit = selectedUnit ?? chartData?.unit;
    }
    const {labels = [], data = []} = tmpChartData || {};
    const unitStr = `(${unit})`;
    const maxValue = Math.max(...data);
    const option = useMemo(() => {
      return {
        splitNumber: 4, // 分段数
        backgroundColor: '#ffffff',
        grid: {
          containLabel: true,
          splitLine: {
            show: true,
            lineStyle: {
              type: 'dotted', // 可以设置为 'solid', 'dashed', 'dotted'
            },
          },
          top: 8,
          left: 20, // x轴下标签第一个元素正常显示，左移20
          height: '90%',
          flex: 1,
          right: 2, // 整个距离右边
        },
        xAxis: {
          axisPointer: {
            lineStyle: {
              color: '#77BC1F',
              width: 2,
              type: 'dashed',
            },
          },
          axisLabel: {
            show: true, // 仅显示第一个和最后一个横坐标日期
            color: '#000000',
            fontSize: 12, // 或其他处理方式，如 'break' 等（根据ECharts版本）
          },
          splitNumber: 4, // 分段数
          boundaryGap: false,
          type: 'category',
          data: labels.map((item: string) => item.replace(/,/g, '\n')),
          axisTick: {
            show: false,
          },
          axisLine: {
            lineStyle: {
              color: '#ccc', // 坐标轴线线的颜色
            },
          },
          splitLine: {
            show: true, // 显示网格线
            lineStyle: {
              type: 'dashed', // 设置为虚线
              color: '#919191',
              width: 0.5,
              opacity: 0.1986,
            },
          },
        },
        yAxis: {
          axisTick: {
            show: false, // 显示刻度
          },
          splitLine: {
            show: true, // 显示网格线
            lineStyle: {
              color: '#919191',
              width: 0.5,
              opacity: 0.1986,
            },
          },
          axisLine: {
            lineStyle: {
              color: '#ccc', // 坐标轴线线的颜色
            },
          },
          fontSize: 12,
          type: 'value',
          position: 'right',
          splitNumber: 4, // 分段数
          max: maxValue,
          min: 0,
          axisLabel: {
            showMaxLabel: true, // 强制显示最大值标签
            show: true, // 隐藏默认的刻度标签
            color: 'rgba(0, 0, 0, 0.5)', // 修改颜色为#000000，透明度为0.5
            fontSize: 12,
            formatter: `function (value,index){
            if (index === 0) {
              return value;
            }
          const max = ${maxValue};
          if (max <= 1) {
            return value.toFixed(2);
          } else if (max > 10) {
            return Math.round(value);
          } else {
            return value.toFixed(1);
          }
        },`,
          },
          show: true,
        },
        series: [
          {
            data: data,
            type: 'line',
            smooth: false, // 启用平滑曲线
            itemStyle: {
              color: '#77BC1F',
            },
            symbol: 'none',
            symbolSize: 3,
            lineStyle: {
              color: '#77BC1F',
              width: 1,
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(119, 188, 31, 0.7)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(119, 188, 31, 0)',
                  },
                ],
              },
            },
          },
        ],
      };
    }, [data, labels, maxValue]);

    useEffect(() => {
      // 创建新的option对象避免直接修改原对象
      const newOption = {
        ...option,
        xAxis: {
          ...option.xAxis,
          data: labels.map((item: string) => item.replace(/,/g, '\n')),
        },
        series: [
          {
            ...option.series[0],
            data: data,
          },
        ],
      };
      echartsDom.current?.setOption(newOption);
    }, [data, labels, option]);

    return data.length === 0 ? (
      <NoContent style={styles.noContentView} />
    ) : (
      <View style={[styles.container, style]}>
        <Text style={styles.unitTitle}>{unitStr}</Text>
        <ECharts ref={echartsDom} option={option} />
      </View>
    );
  },
);

export default BaseLineChart;

const styles = StyleSheet.create({
  noContentView: {
    marginTop: 20,
  },
  container: {
    flex: 1,
  },
  unitTitle: {
    alignSelf: 'flex-end',
    opacity: 0.5,
    fontSize: 12,
    color: '#000',
    paddingRight: 3,
  },
});
