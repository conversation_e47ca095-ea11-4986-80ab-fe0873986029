import React from 'react';
import {View, TouchableOpacity, Text, Platform} from 'react-native';
import {Header as HeaderRNE} from 'react-native-elements';
import CustomStyleSheet from '@utils/style/index.js';
import {LeftArrowIcon, MoreIcon} from '@components/Icon';

export const HeaderView = ({
  style,
  title = '',
  rightTitle,
  onLeftPress = () => {},
  onRightPress = () => {},
  rightElement = null,
  showMore = false,
  mode = 'normal',
}) => {
  const androidProps =
    Platform.OS === 'android'
      ? {
          statusBarProps: {backgroundColor: 'transparent', translucent: true},
          barStyle: 'dark-content',
        }
      : {};
  return (
    <HeaderRNE
      containerStyle={[styles.container, style]}
      leftComponent={
        <View style={styles.headerLeft}>
          <TouchableOpacity
            onPress={onLeftPress}
            hitSlop={{top: 10, right: 10, left: 10, bottom: 10}}>
            <LeftArrowIcon
              strokeColor={mode === 'light' ? '#FFFFFF' : '#3C3936'}
              style={styles.leftIcon}
            />
          </TouchableOpacity>
        </View>
      }
      centerComponent={{text: title, style: styles.heading}}
      rightComponent={
        <View style={styles.headerRight}>
          <TouchableOpacity
            onPress={onRightPress}
            hitSlop={{top: 10, right: 10, left: 10, bottom: 10}}>
            {rightElement ? (
              rightElement
            ) : showMore ? (
              <MoreIcon
                strokeColor={mode === 'light' ? '#FFFFFF' : '#3C3936'}
              />
            ) : (
              rightTitle && <Text style={styles.rightTitle}>{rightTitle}</Text>
            )}
          </TouchableOpacity>
        </View>
      }
      {...androidProps}
    />
  );
};

const styles = CustomStyleSheet.create({
  container: {
    backgroundColor: '#ffffff',
  },
  leftIcon: {
    marginLeft: 5,
  },
  heading: {
    color: '#000000',
    fontSize: 18,
  },
  headerLeft: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerRight: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    color: '#000000',
    paddingRight: 5,
  },
  moreView: {
    width: 11,
    height: 22,
  },
  rightTitle: {
    color: '#000000',
    fontSize: 15,
  },
});
