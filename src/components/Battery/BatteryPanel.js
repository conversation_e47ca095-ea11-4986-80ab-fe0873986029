import React, {memo, useMemo} from 'react';
import Svg, {
  G,
  Path,
  Defs,
  LinearGradient,
  Stop,
  Pattern,
  Use,
  Image,
  Text,
  TSpan,
} from 'react-native-svg';
import {StyleSheet} from 'react-native';
import PropTypes from 'prop-types';

const getPercentValue = percent => {
  const percentage = percent > 100 ? 100 : percent < 0 ? 0 : percent;
  return percentage;
};

const getNumberLength = number => {
  return String(number).length;
};

const calculateHorizontalTranslate = value => {
  const length = getNumberLength(value);
  return value === '--' ? 'translate(-10)' : `translate(${(length - 1) * -20})`;
};

const getPercentPath = percent => {
  const percentValue = percent / 100;
  return `m${50.389 + 276.11 * percentValue} 89.517
  L${20.223 + 333.277 * percentValue} 141.056
  L${21.967 + 331.533 * percentValue} 158.142
  L21.967 156.455
  L20.223 141.056
  L50.389 89.031
  l126.489.486Z`;
};

const faultColor = '#FF4646';
const normalColor = '#71FF7B';
const normalSecondColor = '#44D936';

const BatteryPanel = ({
  width = 375,
  status = 'normal',
  percent = 0,
  maxPercent = 100,
  children,
  ...props
}) => {
  const height = (width * 275) / 375;
  let _percent = percent;
  let _maxPercent = maxPercent;

  if (typeof percent !== 'number') {
    _percent = 0;
    console.warn('props: percent must be a number');
  }

  if (typeof maxPercent !== 'number') {
    _maxPercent = 0;
    console.warn('props: maxPercent must be a number');
  }

  // percent value
  const percentData = useMemo(() => {
    const percentageValue = getPercentValue(_percent);
    const percentPath = getPercentPath(percentageValue);
    const percentLinearGradientX2 = 106.476 + (630.524 * percentageValue) / 100;
    return {
      percentageValue: _percent <= 0 ? '--' : percentageValue,
      percentPath,
      percentLinearGradientX2,
    };
  }, [_percent]);

  // max percent value
  const maxPercentData = useMemo(() => {
    const maxPercentValue = getPercentValue(_maxPercent);
    const maxPercentPath = getPercentPath(maxPercentValue);
    const maxPercentX = 21.967 + (341.533 * maxPercentValue) / 100;
    return {
      maxPercentValue,
      maxPercentPath,
      maxPercentX,
    };
  }, [_maxPercent]);

  return (
    <Svg
      xmlns="http://www.w3.org/2000/svg"
      xmlnsXlink="http://www.w3.org/1999/xlink"
      width={width}
      height={height}
      viewBox="0 0 375 257"
      fill="none"
      {...props}>
      {/* 底部阴影盒子 */}
      <G filter="url(#m)" x={16} y={100}>
        <Use xlinkHref="#j" />
      </G>
      {/* 内部梯形边框 */}
      <G filter="url(#a)">
        <Path
          fill="#2CC17B"
          fillOpacity={0.301}
          fillRule="evenodd"
          d="m324.708 102.955 27.792 53.983-330.5-1.12 29.536-53.88 273.172 1.017Z"
          clipRule="evenodd"
        />
      </G>
      {/* 外部边框 */}
      <Path fill="url(#b)" d="M0 0h375v257H0z" />
      {/* 实际电量盒子 */}
      <G filter="url(#c)">
        <Path
          fill="url(#d)"
          fillRule="evenodd"
          d={percentData.percentPath}
          // d="m326.5 89.517 27 51.539v16.086l-331.533-.687-1.744-15.399 30.166-52.025 276.111.486Z"
          clipRule="evenodd"
        />
      </G>
      {/* white line */}
      <G filter="url(#f)">
        <Path
          stroke="#fff"
          strokeOpacity={0.7}
          d="m354.289 142.222-333.566-1.378L50.75 89.5"
        />
      </G>
      {/* current percentage value */}
      <G transform="translate(95,105)">
        <Text fill="#3E3936" textAnchor="middle" style={styles.percentage}>
          {percentData.percentageValue}
          <TSpan
            style={styles.percentageUnit}
            transform={calculateHorizontalTranslate(
              percentData.percentageValue,
            )}>
            %
          </TSpan>
        </Text>
      </G>
      {children}
      {/* max percent indicator */}
      {maxPercent > 0 && (
        <>
          {/* max percent value: 90% */}
          <Path
            fill="#3E3936"
            d="M310 181.834c.453 0 .883.084 1.289.252.406.164.766.422 1.078.773.316.348.565.795.744 1.342.18.543.27 1.197.27 1.963v.012c0 .949-.137 1.762-.41 2.437-.274.676-.666 1.196-1.178 1.559-.508.359-1.115.539-1.822.539-.52 0-.989-.094-1.407-.281a2.889 2.889 0 0 1-1.031-.785 2.754 2.754 0 0 1-.545-1.149l-.012-.064h1.483l.023.058a1.463 1.463 0 0 0 .821.862c.199.078.422.117.668.117.445 0 .808-.131 1.089-.393.286-.265.499-.619.639-1.06.145-.446.227-.938.246-1.477.004-.059.006-.115.006-.17v-.17l-.264-1.412c0-.32-.074-.611-.222-.873a1.675 1.675 0 0 0-.604-.621 1.602 1.602 0 0 0-.849-.229c-.305 0-.584.075-.838.223a1.703 1.703 0 0 0-.61.609c-.152.254-.228.54-.228.856v.012c0 .328.072.619.217.873.144.25.341.447.591.592.25.144.536.216.856.216.32 0 .607-.07.861-.211.254-.14.455-.336.604-.586.148-.25.222-.533.222-.849v-.012h.352v1.559h-.205a2.059 2.059 0 0 1-.451.621c-.2.191-.446.345-.739.463a2.704 2.704 0 0 1-1.019.175c-.539 0-1.016-.121-1.43-.363a2.628 2.628 0 0 1-.978-.996 2.887 2.887 0 0 1-.352-1.43v-.011c0-.575.133-1.084.399-1.53.269-.449.64-.8 1.113-1.054.476-.258 1.017-.387 1.623-.387Zm8.382 8.877c-.672 0-1.248-.18-1.729-.539-.476-.36-.843-.871-1.101-1.535-.258-.664-.387-1.451-.387-2.362v-.011c0-.91.129-1.696.387-2.356.258-.664.625-1.176 1.101-1.535.481-.359 1.057-.539 1.729-.539.672 0 1.248.18 1.729.539.48.359.849.871 1.107 1.535.258.66.387 1.446.387 2.356v.011c0 .911-.129 1.698-.387 2.362-.258.664-.627 1.175-1.107 1.535-.481.359-1.057.539-1.729.539Zm0-1.225c.359 0 .666-.129.92-.386.254-.258.447-.625.58-1.102.137-.48.205-1.055.205-1.723v-.011c0-.668-.068-1.241-.205-1.717-.133-.481-.326-.848-.58-1.102a1.247 1.247 0 0 0-.92-.386c-.359 0-.666.129-.92.386-.25.254-.443.621-.58 1.102-.133.476-.199 1.049-.199 1.717v.011c0 .668.066 1.243.199 1.723.137.477.33.844.58 1.102.254.257.561.386.92.386Zm6.935-2.877c-.395 0-.735-.093-1.02-.281a1.839 1.839 0 0 1-.65-.814c-.152-.352-.229-.768-.229-1.248v-.006c0-.481.077-.895.229-1.242.152-.352.369-.622.65-.809.285-.188.625-.281 1.02-.281.394 0 .732.093 1.014.281.285.187.503.457.656.809.152.347.228.761.228 1.242v.006c0 .48-.076.896-.228 1.248a1.83 1.83 0 0 1-.656.814c-.282.188-.62.281-1.014.281Zm0-.849c.265 0 .473-.129.621-.387.148-.262.223-.631.223-1.107v-.006c0-.473-.075-.838-.223-1.096-.148-.258-.356-.387-.621-.387-.27 0-.479.129-.627.387-.145.258-.217.623-.217 1.096v.006c0 .476.072.845.217 1.107.148.258.357.387.627.387Zm1.055 4.74h-1.207l5.712-8.455h1.207l-5.712 8.455Zm5.554.117c-.39 0-.728-.094-1.013-.281a1.827 1.827 0 0 1-.657-.809c-.152-.351-.228-.767-.228-1.248v-.006c0-.484.076-.9.228-1.248.153-.351.371-.621.657-.808.285-.188.623-.281 1.013-.281.395 0 .735.093 1.02.281.285.187.504.457.656.808.152.348.229.764.229 1.248v.006c0 .481-.077.897-.229 1.248a1.825 1.825 0 0 1-.656.809c-.285.187-.625.281-1.02.281Zm0-.849c.27 0 .477-.129.621-.387.149-.262.223-.629.223-1.102v-.006c0-.476-.074-.843-.223-1.101-.144-.258-.351-.387-.621-.387-.265 0-.472.129-.621.387-.144.258-.217.625-.217 1.101v.006c0 .473.073.84.217 1.102.149.258.356.387.621.387Z"
          />
          {/* max percent triangle */}
          <Path
            fill="#3E3936"
            fillRule="evenodd"
            d="m320 169 4 8h-8l4-8Z"
            clipRule="evenodd"
          />
          {/* 最大保护电量盒子 */}
          <Path
            fill="url(#e)"
            d={maxPercentData.maxPercentPath}
            // d="M293 89H50.5L21 140l1 17h285v-15l-14-53Z"
          />
        </>
      )}
      <Defs>
        <LinearGradient
          id="d"
          x1={27.5}
          x2={301}
          y1={149.25}
          y2={149.25}
          gradientUnits="userSpaceOnUse">
          {/* <Stop stopColor="#71FF7B" stopOpacity={0.3} />
          <Stop offset={1} stopColor="#44D936" stopOpacity={0.85} /> */}
          <Stop
            stopColor={status === 'fault' ? faultColor : normalColor}
            stopOpacity={status === 'fault' ? 0.04 : 0.3}
          />
          <Stop
            offset={1}
            stopColor={status === 'fault' ? faultColor : normalSecondColor}
            stopOpacity={status === 'fault' ? 0.9 : 1}
          />
        </LinearGradient>
        <LinearGradient
          id="e"
          x1={300}
          x2={31}
          y1={126.5}
          y2={123}
          gradientUnits="userSpaceOnUse">
          <Stop stopColor="#91FF87" stopOpacity={0.4} />
          <Stop offset={0.997} stopColor="#91FF87" stopOpacity={0} />
        </LinearGradient>
        {/* 背景边框盒子图片 */}
        <Pattern
          id="b"
          width={1}
          height={1}
          patternContentUnits="objectBoundingBox">
          <Use xlinkHref="#g" transform="scale(.00133 .00195)" />
        </Pattern>
        <Image
          xlinkHref="data:image/png;base64,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"
          id="g"
          width={750}
          height={514}
        />
        <Image
          xlinkHref="data:image/png;base64,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"
          id="j"
          width={345}
          height={70}
        />
      </Defs>
    </Svg>
  );
};

BatteryPanel.propTypes = {
  width: PropTypes.number,
  percent: PropTypes.number,
  maxPercent: PropTypes.number,
  status: PropTypes.oneOf(['normal', 'fault']),
  children: PropTypes.node,
};

export default memo(BatteryPanel);

const styles = StyleSheet.create({
  percentage: {
    fontSize: 60,
    fontWeight: '500',
  },
  percentageUnit: {
    fontSize: 29,
    fontWeight: '500',
  },
});
