import React from 'react';
import {Text, View, StyleSheet, ScrollView} from 'react-native';
import {inject, observer} from 'mobx-react/native';
import PageView from '@components/PageView.js';
import PageBase from '@base/index.js';
import {Cell, HeaderView, WhiteSpace} from '@components';
import {withTranslation, useTranslation} from 'react-i18next';
import {geti18nText} from '@i18n/config';
import {isIphoneX} from '@utils/device';

/**
 * @typedef {import('@/mobx').panelStores} panelStores
 * @typedef {import('react-i18next').TFunction} TFunction
 */

/**
 * @typedef {Object} InjectedProps
 * @property {panelStores} panel - MobX Store
 * @property {TFunction} t - i18next translation function
 */

@inject('panel')
@observer
class Statistics extends PageBase {
  /**
   * @type {InjectedProps}
   */
  props;

  componentDidMount() {}

  render() {
    const {t} = this.props;
    const {totalChargingTime} = this.props.panel.usageStatistics;

    return (
      <PageView>
        <HeaderView
          title={t(geti18nText('statistics_title_textview_text'))}
          onLeftPress={() => {
            this.goBack();
          }}
        />
        <ScrollView>
          <DataListCard totalChargingTime={totalChargingTime} />
        </ScrollView>

        <WhiteSpace size={isIphoneX ? 34 : 0} />
      </PageView>
    );
  }
}

const DataListCard = ({totalChargingTime}) => {
  const isValidValue = value => value >= 0;
  const dataFormat = (item, unit = '') => {
    return isValidValue(item) ? `${item}${unit}` : '--';
  };
  const {t} = useTranslation('all');
  const list = [
    {
      key: 'chargingTime',
      title: t(geti18nText('statistics_chargingTime_textview_text')),
      rightElement: (
        <View style={styles.rightBox}>
          <Text numberOfLines={1} ellipsizeMode="tail" style={styles.extraText}>
            {dataFormat(totalChargingTime, 'hr')}
          </Text>
        </View>
      ),
    },
  ];
  const size = list.length;
  return (
    <View style={styles.container}>
      {list.map((item, index) => (
        <Cell
          key={item.key}
          title={item.title}
          iconShow={false}
          rightElement={item.rightElement}
          containerStyle={styles.containerStyle}
          hideRightArrow={true}
          showWholeUnderline={index + 1 < size}
        />
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginTop: 10,
    borderRadius: 10,
    marginHorizontal: 15,
    overflow: 'hidden',
    backgroundColor: '#FFFFFF',
  },
  rightBox: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  extraText: {
    color: '#666666',
    fontSize: 13,
    fontWeight: '400',
    lineHeight: 16,
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  containerStyle: {
    marginHorizontal: 17,
  },
});

export default withTranslation('all')(Statistics);
