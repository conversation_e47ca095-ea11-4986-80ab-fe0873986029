/*
 * @Description: 消息处理
 */

import {CommonEmitter, mobile} from 'cvn-panel-kit';
import {LogUtils, MessageUtils} from 'cvn-panel-kit/src/utils';
import dayjs from 'dayjs';
import {
  fetchDeviceErrorList,
  fetchHomeMessageList,
  getPartsOverview,
  usageHistory,
} from 'IOTRN/src/api';
import DeviceStore from 'IOTRN/src/mobx/deviceStore';
import DeviceConfigurationStore from 'IOTRN/src/mobx/deviceConfigurationStore';
import DeviceCharStore from 'IOTRN/src/mobx/deviceChartStore';
import DeviceAbnormalStore from 'IOTRN/src/mobx/deviceAbnormalStore';
import DeviceConnectionStore from 'IOTRN/src/mobx/deviceConnectionStore';
import {RootStore} from 'IOTRN/src/mobx/rootStore';
import {EntryProps} from 'IOTRN/src/mobx/utils/interfaceProps';
import {BUSTYPEDEFAULTVALUE} from 'IOTRN/src/utils/constant';
import logger from '../../../utils/logger';

export default class MessageProcess {
  deviceStore: DeviceStore;
  deviceConfigurationStore: DeviceConfigurationStore;
  deviceAbnormalStore: DeviceAbnormalStore;
  deviceConnectionStore: DeviceConnectionStore;
  deviceChartStore: DeviceCharStore;
  rootStore;
  constructor(store: RootStore) {
    this.rootStore = store;
    this.deviceStore = store.deviceStore;
    this.deviceConfigurationStore = store.deviceConfigurationStore;
    this.deviceAbnormalStore = store.deviceAbnormalStore;
    this.deviceConnectionStore = store.deviceConnectionStore;
    this.deviceChartStore = store.deviceChartStore;
  }

  /**
   * error list接口，用于status小红点
   */
  requestErrorStatusList = () => {
    const params = {
      deviceId: this.deviceStore.state.initialParams.deviceId,
    };
    fetchDeviceErrorList(params).then(
      (res: {entry: {[key: string]: string}[]}) => {
        this.deviceAbnormalStore.actions.setErrorStatusList(res?.entry);
      },
    );
  };

  /**
   * 故障消息列表
   */
  requestHomeMsgList = () => {
    const params = {
      deviceId: this.deviceStore.state.initialParams.deviceId,
    };
    fetchHomeMessageList(params).then((res: {entry: []}) => {
      this.deviceAbnormalStore.actions.setHomeMsgList(res?.entry);
    });
  };

  /**
   * @description: 处理设备报警消息
   */
  dealWithMsg = () => {
    MessageUtils.dealWithMessage(
      {deviceId: this.deviceStore.state.initialParams.deviceId},
      (
        msgType: number,
        resObj: {
          pushTypes?: number[];
          title: string;
          payloadData: {
            uuid: string;
            productId: string;
            messageType: string;
            createTime: string;
            deviceId: string;
            eventType: string;
            data: string;
          };
        },
      ) => {
        logger.warn('告警消息--设备消息体-', JSON.stringify(resObj));
        // 只处理设备消息 2
        if (msgType !== 2) {
          return;
        }
        // status 小红点处理
        this.deviceAbnormalStore.actions.dealWithErrorStatus(resObj);
        const {pushTypes = []} = resObj;
        // 不包含 2：banner类型和9：全清
        const hasNineOrTwo = pushTypes.includes(9) || pushTypes.includes(2);
        if (!hasNineOrTwo) {
          return;
        }
        // 根据推送消息，对homeMsgList 做实时的更新，增加或者减少
        this.deviceAbnormalStore.actions.dealWithMessageRes(resObj);
      },
    );
  };

  /**
   * @description: 消息推送监听处理
   */
  dealWithPushNotification = () => {
    CommonEmitter.addListener('didRecievePushNotification', () => {
      if (LogUtils.currentPage !== 'ViewPanelHome') {
        if (mobile.handlePushNotification) {
          mobile.handlePushNotification();
        }
      }
    });
  };

  /**
   * @description: 配件接口，用于显示配件数量
   */
  dealWithPartsDetail = () => {
    const params = {
      req: this.deviceStore.state.initialParams.deviceId,
    };
    getPartsOverview(params)
      .then((res: {entry?: {number: number; type: number}}) => {
        const partsDetail = res?.entry || null;
        if (partsDetail) {
          this.deviceConnectionStore.actions.setPartsDetail(partsDetail);
        }
      })
      .catch((error: string) => {
        console.error('Error fetching parts detail:', error);
        // Handle error appropriately
      });
  };

  /**
   * @description: 数据统计二氧化碳总排放量
   * @param {String} deviceId
   */
  getTotalCo2Reduced = () => {
    // 首页默认是日维度
    const date = dayjs();
    const formattedDate = date.format('YYYY/MM/DD');
    const params = {
      dateType: 1, // 请求的日期类型：1:日期，2:周，3:月份
      dateValue: formattedDate, // 周场景入参： "2024/01/05-2024/01/10" 月场景入参： "2024/01"
      busType: BUSTYPEDEFAULTVALUE.c02Reduction, // 1 查询的业务数据类型：1：工作时长 2：二氧化碳减排量 3：割草面积 }
      deviceId: this.deviceStore.state.initialParams.deviceId,
      datePeriod: 7,
    };
    usageHistory(params).then((res: {entry: EntryProps}) => {
      const {entry} = res;
      this.deviceChartStore.actions.setStaticsTotalCo2Reduced(entry);
    });
  };

  /**
   * @description: 历史数据接口
   * @param {String} deviceId
   */
  getUsageHistory = () => {
    // 首页默认是日维度
    const date = dayjs();
    const formattedDate = date.format('YYYY/MM/DD');
    const params = {
      dateType: 1, // 请求的日期类型：1:日期，2:周，3:月份
      dateValue: formattedDate, // 周场景入参： "2024/01/05-2024/01/10" 月场景入参： "2024/01"
      busType: BUSTYPEDEFAULTVALUE.mowingTime, // 1 查询的业务数据类型：1：工作时长 2：二氧化碳减排量 3：割草面积 }
      deviceId: this.deviceStore.state.initialParams.deviceId,
      datePeriod: 7,
    };
    usageHistory(params).then((res: {entry: EntryProps}) => {
      const {entry} = res;
      this.deviceChartStore.actions.setHomeUsageHistoryData(entry);
    });
  };

  // 处理模型数据 loading 逻辑
  handleModelDataLoading = (reported = {}) => {
    if (Object.keys(reported).length > 0) {
      const currentKeys = Object.keys(reported);
      const {currentModelKeys = []} = this.deviceConnectionStore.state;
      const sameArray = currentKeys.filter(item =>
        currentModelKeys.includes(item),
      );
      if (sameArray.length > 0) {
        mobile.hideLoading();
      }
    }
  };
}
