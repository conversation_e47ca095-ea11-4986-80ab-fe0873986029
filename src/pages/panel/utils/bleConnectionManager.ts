/*
 * @FilePath: /61004/src/pages/panel/utils/bluetoothCommunication
 * @Description: 牙蓝连接处理方法工具类
 */

import Strings from '@i18n';

import {device, CommonEmitter, mobile} from 'cvn-panel-kit';
import {Platform} from 'react-native';
import {tracer, eleIdMap, pageIdMap} from '@tracer';
import {propertyMap} from '@utils/model';
import {OtaUtils} from 'cvn-panel-kit/src/utils';
import {isObject} from 'lodash-es';
import DeviceConnectionStore from 'IOTRN/src/mobx/deviceConnectionStore';
import DeviceStore from 'IOTRN/src/mobx/deviceStore';
import DeviceConfigurationStore from 'IOTRN/src/mobx/deviceConfigurationStore';
import DeviceCharStore from 'IOTRN/src/mobx/deviceChartStore';
import BatteryManagementStore from 'IOTRN/src/mobx/batteryStore';
import {RootStore} from 'IOTRN/src/mobx/rootStore';
import {parseJsonSafely} from '../../../utils/jsonParser';

const MAX_RETRY_ATTEMPTS = 3; // 最大重连次数

// 设备蓝牙连接状态
enum BleConnectStatus {
  BLE_CONNECT_STATUS_DISCONNECTED = 0, // 蓝牙断开连接
  BLE_CONNECT_STATUS_CONNECTING = 1, // 蓝牙连接中
  BLE_CONNECT_STATUS_CONNECTED = 2, // 蓝牙已连接
  BLE_CONNECT_STATUS_CONNECT_FAILED = 3, // 蓝牙连接失败
}
/**
 * @description: 手机蓝牙开启状态
 */
enum BleOpenStatus {
  BLE_OPEN_STATUS_NOT_OPENED = 0, // 蓝牙未开启
  BLE_OPEN_STATUS_OPENED = 1, // 蓝牙已开启
  BLE_OPEN_STATUS_UNAUTHORIZED = 2, // 蓝牙未授权
}

export {BleConnectStatus, BleOpenStatus};

/**
 * @description: 蓝牙连接相关
 */
export default class BleConnectionManager {
  deviceConnectionStore: DeviceConnectionStore;
  deviceStore: DeviceStore;
  deviceConfigurationStore: DeviceConfigurationStore;
  deviceChartStore: DeviceCharStore;
  batteryManagementStore: BatteryManagementStore;
  sendCmdtimer: NodeJS.Timeout | number = 0;
  retryTimes: number = 0; // 重连次数
  otaUpdateDidChecked: boolean = false; // 是否已经检查过ota升级
  showForceUpdateDialog: boolean = false; // 是否显示强制升级对话框
  constructor(store: RootStore) {
    this.deviceConnectionStore = store.deviceConnectionStore;
    this.deviceStore = store.deviceStore;
    this.deviceConfigurationStore = store.deviceConfigurationStore;
    this.deviceChartStore = store.deviceChartStore;
    this.batteryManagementStore = store.batteryManagementStore;
  }

  /**
   * @description 处理蓝牙连接状态和吐司提示
   * @param {Object} res
   */
  handleBleConnectAndToast = (res: number, tmpMac: string) => {
    switch (res) {
      case BleOpenStatus.BLE_OPEN_STATUS_NOT_OPENED:
        {
          const title = Strings.getLang(
            'rn_common_ble_notopened_textview_text',
          );
          mobile.toast(title, () => {});
          this.deviceConnectionStore.actions?.setBleConnect(false);
        }
        break;
      case BleOpenStatus.BLE_OPEN_STATUS_OPENED:
        {
          const title = Strings.getLang('rn_common_ble_opened_textview_text');
          mobile.toast(title, () => {});
          // 触发重连逻辑
          this.retryTimes = 0;
          // 手机蓝牙开启成功后，手机主动去链接设备
          device.requestBleConnect(tmpMac);
        }
        break;
      case BleOpenStatus.BLE_OPEN_STATUS_UNAUTHORIZED:
        {
          const title = Strings.getLang(
            'rn_common_ble_notauthorizedtitle_textview_text',
          );
          mobile.toast(title, () => {});
          this.deviceConnectionStore.actions.setBleConnect(false);
        }
        break;
      default:
        break;
    }
  };

  /**
   * @description: 蓝牙连接相关
   */
  dealWithBleConnect = () => {
    const tmpMac = this.deviceStore.state.initialParams.mac;
    // 监听手机蓝牙的开关
    CommonEmitter.addListener('bluetoothChange', (res: string) => {
      const resNum = Number(res);
      // 点击返回按钮后，该回调不走，解决IOT-12452
      // 返回键状态一直为true，需要考虑这个返回键状态是否需要关联蓝牙
      if (!this.deviceConnectionStore.state.isInRNContainerVC) {
        return;
      }
      // 防止重复返回同一个状态
      const {currentBleState} = this.deviceConnectionStore.state;
      if (currentBleState === resNum) {
        return;
      }
      // 0 未开启，1已开启，2 未授权
      this.handleBleConnectAndToast(resNum, tmpMac);
      this.deviceConnectionStore.actions.setCurrentBleState(resNum);
    }); // 监听设备的蓝牙状态变化
    CommonEmitter.addListener('deviceBleConnectStateChange', (res: string) => {
      // res: 0:notconnected  1:connecting  2:connected  3:连接失败
      this.dealWithBleDataChange(res);
    });
    /**
     * 第一次进去调手机蓝牙连接设备蓝牙请求,
     * 该请求必须在监听事件后面执行
     * */
    device.requestBleConnect(tmpMac);
  };

  /**
   * @description: 处理蓝牙数据上报
   * @param {*} res
   */
  dealWithBleDataChange = (res: string) => {
    const tmpConnectStatus = Number(res);
    /**
     * @description: 为防止蓝牙瞬间连接再断开，显示切换成蓝牙通道再切换成WIFI通道，
     * 造成数据闪烁，暂增加逻辑，每次方法回来记录最新连接状态
     */
    this.deviceConnectionStore.actions.setLatestBleConnectedStatus(
      tmpConnectStatus,
    );
    if (tmpConnectStatus === BleConnectStatus.BLE_CONNECT_STATUS_CONNECTED) {
      this.getOnceData();
      // ota检查只在第一次进入面板后，蓝牙连接成功调取一次，3.31兼容iOS ota升级成功回来，因查询版本号设备未返回结果的问题
      if (!this.otaUpdateDidChecked) {
        this.dealWithOtaUpdatePureBleTooth();
        this.otaUpdateDidChecked = true;
      }
      this.deviceConnectionStore.actions.setBleConnectStatus(
        BleConnectStatus.BLE_CONNECT_STATUS_CONNECTED,
      );
      this.deviceConnectionStore.actions.setBleConnect(true);
      // 重置重连次数
      this.retryTimes = 0;
      // 弹连接成功的吐司
      mobile.toast(
        Strings.getLang('rn_common_ble_connected_textview_text'),
        () => {},
      );
    } else {
      const {isNativeGoback} = this.deviceConnectionStore.state;
      // 返回到原生vc，不处理重连逻辑
      if (isNativeGoback) {
        return;
      }
      if (Platform.OS === 'android') {
        if (
          tmpConnectStatus ===
            BleConnectStatus.BLE_CONNECT_STATUS_CONNECT_FAILED ||
          tmpConnectStatus === BleConnectStatus.BLE_CONNECT_STATUS_DISCONNECTED
        ) {
          this.dealWithUnConnect();
        }
      } else if (
        tmpConnectStatus !== BleConnectStatus.BLE_CONNECT_STATUS_CONNECTED
      ) {
        // iOS按照超时时间来判断，超时10s认为就是连接失败
        this.dealWithUnConnect();
      }
      this.handleRetryStatus(tmpConnectStatus);
    }
  };

  /**
   * @description 重连三次状态设置
   * @param {*} tmpConnectStatus
   */
  handleRetryStatus = (tmpConnectStatus = 0) => {
    if (this.retryTimes > MAX_RETRY_ATTEMPTS) {
      this.deviceConnectionStore.actions.setBleConnectStatus(
        MAX_RETRY_ATTEMPTS,
      );
    } else {
      this.deviceConnectionStore.actions.setBleConnectStatus(tmpConnectStatus);
    }
    this.deviceConnectionStore.actions.setBleConnect(false);
  };

  /**
   * @description: 连接失败处理
   */
  dealWithUnConnect = () => {
    this.retryTimes++;
    // 超过3次重连失败，停止重连,RN端标记为连接失败
    if (this.retryTimes > MAX_RETRY_ATTEMPTS) {
      mobile.toast(
        `${Strings.getLang('rn_common_ble_reconnecterror_textview_text')}`,
        () => {},
      );
      if (
        this.deviceConnectionStore.state.latestBleConnectedStatus ===
        BleConnectStatus.BLE_CONNECT_STATUS_CONNECTING
      ) {
        const {bleConnected, bleConnectedStatus} =
          this.deviceConnectionStore.state;
        // 防止重复赋值
        if (
          bleConnectedStatus ===
            BleConnectStatus.BLE_CONNECT_STATUS_CONNECT_FAILED &&
          !bleConnected
        ) {
          return;
        }
        this.deviceConnectionStore.actions.setBleConnectStatus(
          BleConnectStatus.BLE_CONNECT_STATUS_CONNECT_FAILED,
        );
        this.deviceConnectionStore.actions.setBleConnect(false);
      }
      return;
    }
    device.requestBleConnect(this.deviceStore.state.initialParams.mac);
  };

  /**
   * @description: ota纯蓝牙升级逻辑
   */
  dealWithOtaUpdatePureBleTooth = () => {
    try {
      const {deviceId, mac} = this.deviceStore.state.initialParams;
      OtaUtils.dealWithUpdate(
        {deviceId, mac, isBleDevice: true, singleMcu: true},
        (res: {
          isForceUpdate: boolean;
          showRed: boolean;
          customVersion: string;
        }) => {
          const {isForceUpdate, showRed, customVersion} = res;
          // 有ota更新，则新版本号，从这里取，如果无ota更新，从device/detail拿对应的字段
          this.deviceConfigurationStore.actions.setShowRed(showRed);
          this.deviceConfigurationStore.actions.setIsForceUpdate(isForceUpdate);
          if (customVersion) {
            this.deviceConfigurationStore.actions.setCustomVersion(
              customVersion,
            );
          }
          this.handleAlertAndDot();
        },
      );
    } catch (error) {
      console.error('Error in dealWithOtaUpdatePureBleTooth:', error);
      // Handle error appropriately
    }
  };

  /**
   * @description: 升级弹框和小红点
   * 61004暂无此需求，等后续追加再调试，代码勿删
   * @param {Boolean} isForce
   * @param {Boolean} showRed
   */
  handleAlertAndDot = () => {
    const {connected} = this.deviceConnectionStore.state;
    const {showRed, isForceUpdate} = this.deviceConfigurationStore.state;
    // 4g离线，不弹升级框
    if (
      connected &&
      isForceUpdate &&
      this.deviceConnectionStore.state.isInRNContainerVC
    ) {
      // 最新方法
      this.newOtaAlert();
    } else if (
      isForceUpdate &&
      this.deviceConnectionStore.state.isInRNContainerVC
    ) {
      if (this.showForceUpdateDialog) {
        return;
      }
      this.showForceUpdateDialog = true;
      mobile.simpleConfirmDialog(
        ' ',
        Strings.getLang('rn_tractor_panelhome_otaalertmsg_textview_text'),
        () => {
          this.showForceUpdateDialog = false;
          if (this.deviceConnectionStore.state.bleConnected) {
            const {deviceId} = this.deviceStore.state.initialParams;
            const url = `ChervonIot://EGO/DeviceManage/OTAInfo?deviceId=${deviceId}&singleMcu=${true}&upgradeModel=${1}&isSubset=${false}&deviceWifiIsOnline=${connected}`;
            mobile.jumpTo(url, () => {});
          } else {
            mobile.toast(
              Strings.getLang('rn_common_ble_error_textview_text'),
              () => {},
            );
          }
        },
        () => {
          this.showForceUpdateDialog = false;
          this.dealWithGoBack();
        },
      );
    }

    if (showRed) {
      this.deviceConfigurationStore.actions.setUpdateText(
        Strings.getLang('rn_61004_panelhome_updatetitle_textview_text'),
      );
    }
  };

  /**
   * @description: ota alert
   */
  newOtaAlert = () => {
    if (mobile.simpleDialog) {
      if (this.showForceUpdateDialog) {
        return;
      }
      this.showForceUpdateDialog = true;
      mobile.simpleDialog(
        '',
        Strings.getLang('rn_61004_panelhome_otaalertmsg_textview_text'),
        Strings.getLang('rn_61004_panelhome_otacancel_textview_text'),
        Strings.getLang('rn_61004_panelhome_otaupgrade_textview_text'),
        () => {
          this.showForceUpdateDialog = false;
          tracer.click({
            eleid: eleIdMap.Update_App_Window_Update_Button_Click,
            pageid: pageIdMap.updateAlert,
          });
          const {connected} = this.deviceConnectionStore.state;
          const {deviceId} = this.deviceStore.state.initialParams;
          const url = `ChervonIot://EGO/DeviceManage/OTAInfo?deviceId=${deviceId}&singleMcu=${true}&upgradeModel=${0}&isSubset=${false}&deviceWifiIsOnline=${connected}`; // isSubset = false, 通用，只有C的电池是子设备 这里设置为true，其他都是主设备
          mobile.jumpTo(url, () => {});
        },
        () => {
          this.showForceUpdateDialog = false;
          tracer.click({
            eleid: eleIdMap.Update_App_Window_Later_Button_Click,
            pageid: pageIdMap.updateAlert,
          });
          this.dealWithGoBack();
        },
      );
    }
  };

  /**
   * @description: goback处理
   * @param {String} key
   */
  dealWithGoBack = () => {
    this.deviceConnectionStore.actions.setIsNativeGoback(true);
    mobile.back();
  };

  /**
   * @description: 蓝牙通道单次获取总数据
   */
  getOnceData = () => {
    this.sendCmdtimer = setTimeout(() => {
      const cmdHex = '55AA00080000F6FE';
      const {mac} = this.deviceStore.state.initialParams;
      device.sendCmd(cmdHex, mac);
      // 获取设备wifi信息和蓝牙信息方法
      this.deviceConnectionStore.actions.getDeviceWifiInfo();
    }, 2000); // 4.7日 2s --> 0.5s
  };

  /**
   * @description: 清除定时器
   */
  clearTimer = () => {
    if (this.sendCmdtimer) {
      clearTimeout(this.sendCmdtimer);
    }
  };

  /**
   * @description: 蓝牙数据监听
   */
  dealWithBleListener = () => {
    // 蓝牙单次获取或者主动上报都通过该方法返回
    CommonEmitter.addListener('localDeviceDataChange', (res: string) => {
      mobile.hideLoading();
      // 添加蓝牙数据处理
      this.batteryManagementStore.actions.parseBlueToothBatteryData(res);
      // 和本地汇总
      this.deviceConnectionStore.actions.handleBleRes(res);
      // 只处理新上报数据，带callBack
      this.deviceConnectionStore.actions.handleNewBleRes(
        res,
        (data: {[key: string]: string}) => {
          this.handleResLoading(data);
        },
      );
    });
  };

  /**
   * 处理loading
   * @param {Object} newData - 新的数据对象，包含响应结果
   */
  handleResLoading = (newData: object) => {
    // 如果新数据为空，直接返回
    if (Object.keys(newData).length === 0) {
      return;
    }

    // 获取当前数据的键
    const currentKeys = Object.keys(newData);
    const {currentModelKeys} = this.deviceConnectionStore.state;

    // 找出新数据和当前模型键的交集
    const sameArray = currentKeys.filter(item =>
      currentModelKeys.includes(item),
    );

    // 如果有交集，处理loading
    if (sameArray.length > 0) {
      mobile.hideLoading();
      // 置空当前模型键，防止自发上报时吐司提示
      this.deviceConnectionStore.actions.setCurrentModelKeys([]);
    }
  };

  /**
   * @description: 蓝牙cmd数据监听
   */
  dealWithBleCmdListener = () => {
    // 通过发指令获取数据
    CommonEmitter.addListener('otherCmdResponse', (res: string[]) => {
      this.batteryManagementStore.actions.parseBlueToothWifiData(res);
    });
  };

  /**
   * @description: ota升级逻辑
   */
  dealWithOtaUpdate = () => {
    const {connected} = this.deviceConnectionStore.state;
    const {deviceId, mac} = this.deviceStore.state.initialParams;
    if (!connected) {
      return;
    }
    OtaUtils.dealWithUpdate(
      {deviceId, mac, isBleDevice: false, singleMcu: true},
      (res: {
        isForceUpdate: boolean;
        showRed: boolean;
        customVersion: string;
      }) => {
        const {isForceUpdate, showRed, customVersion} = res;
        this.deviceConfigurationStore.actions.setShowRed(showRed);
        this.deviceConfigurationStore.actions.setIsForceUpdate(isForceUpdate);
        if (customVersion) {
          this.deviceConfigurationStore.actions.setCustomVersion(customVersion);
        }
        this.handleAlertAndDot();
      },
    );
  };

  /**
   * @description: topic data数据监听
   */
  dealWithTopic = () => {
    const {deviceId} = this.deviceStore.state.initialParams;
    CommonEmitter.addListener('topicDataDidChange', (res: string) => {
      if (res === '' || !res) {
        return;
      }
      // 使用安全的JSON解析，提供默认值和错误处理
      const obj = parseJsonSafely(
        res,
        {type: '', payload: ''} as {type: string; payload: string},
        'BleConnectionManager.dealWithTopic',
      );
      // 过滤不是当前设备上报的数据,R适用，C有子设备不适用，RN做了兼容
      if (obj.type.indexOf(deviceId) === -1) {
        return;
      }
      const {payload} = obj;
      if (payload) {
        // 使用安全的JSON解析，提供默认值和错误处理
        const payloadData: {
          state: {reported: {[key: string]: {[key: string]: string}}};
        } = parseJsonSafely(
          payload,
          {state: {reported: {}}} as {
            state: {reported: {[key: string]: {[key: string]: string}}};
          },
          'BleConnectionManager.dealWithTopic.payload',
        );
        // 去除loading,等reported回来，更严谨
        const reported: Record<string, object> = payloadData?.state?.reported;
        // 判断数据为空时，不走以下方法
        if (!isNonEmptyObject(reported)) {
          return;
        }
        // 有经纬度上报，处理
        if (reported[propertyMap.gps_coordinate]) {
          this.deviceChartStore.actions.handlePosition(reported);
        }
        const newData: {[key: string]: object} = {};
        for (const key in reported) {
          let result = reported[key];
          if (isObject(reported[key])) {
            result = (reported as {[key: string]: {[key: string]: object}})[
              key
            ]['1'];
          }
          newData[key] = result;
        }
        this.deviceConnectionStore.actions.handleTopicRes(obj);
        this.handleResLoading(newData); // 处理上报数据
      }
    });
  };
}
// 判断一个对象是否为非空对象
function isNonEmptyObject(obj: object): obj is object {
  // 判断对象是否不为null，不为undefined，且类型为object，且对象键的数量大于0
  return (
    obj !== null &&
    obj !== undefined &&
    typeof obj === 'object' &&
    Object.keys(obj).length > 0
  );
}
