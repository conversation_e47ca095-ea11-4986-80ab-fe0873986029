import React from 'react';
import {mobile, CommonEmitter, device} from '@cvn/rn-panel-kit';
import {OtaUtils, LogUtils, MessageUtils} from '@cvn/rn-panel-kit/src/utils';
import PageBase from '@base/index.js';
import PageView from '@components/PageView.js';
import {HeaderView, WhiteSpace} from '@components';
import {isIphoneX} from '@utils/device/index.js';
import {Text, View, Platform, Animated, TouchableOpacity} from 'react-native';
import {inject, observer} from 'mobx-react/native';
import CustomStyleSheet from '@utils/style/index.js';
import topicMap from '@config/topic';
import {
  TopStatusView,
  ChargingOverview,
  Notification,
  UpdateItem,
  OfflinePopUp,
  FunctionSetting,
} from '@pages/panel/components';
import {withTranslation} from 'react-i18next';
import {geti18nText} from '@i18n/config';
import {throttle} from 'lodash-es';

/**
 * @typedef {import('@/mobx').panelStores} panelStores
 * @typedef {import('@/mobx').panelActions} panelActions
 * @typedef {import('react-i18next').TFunction} TFunction
 * @typedef {import('@config/types').MessageData} MessageData
 */

/**
 * @typedef {Object} InjectedProps
 * @property {panelStores} panel - MobX Store
 * @property {panelActions} panelActions - Mobx Store Action
 * @property {TFunction} t - i18next translation function
 */

@inject('panelActions', 'panel')
@observer
class Home extends PageBase {
  /**
   * @type {InjectedProps}
   */
  props;

  constructor(props) {
    super(props);
    this.state = {
      showPicker: false,
      headerMode: 'normal',
      headerOpacity: 0,
      navHeaderHeight: 80,
    };

    this.fromOtaDone = false;

    this.scrollY = new Animated.Value(0);
    this.scrollYListenerId = null;

    LogUtils.init();
  }

  componentDidMount() {
    // 处理 topic subscribe
    this.dealWithTopicSubscribe();

    // 监听原生->RN， RN->原生
    this.dealWithRNContainerPushOrPop();

    // 获取设备详情
    this.dealWithFetchDeviceDetail();

    // 监听topic变化
    this.dealWithTopicChange();

    // publish message to iot-core topic
    this.dealWithTopicPublish();

    // 监听原生ota完成按钮
    this.dealWithNativeOtaDone();

    // 检查ota升级信息
    this.checkOtaUpdateAndForceAlert();

    // 处理安卓物理返回键监听
    this.dealWithAndroidBack();

    this.dealWithScrollListener();
    // 获取首页故障信息
    this.dealWithFaultInfo();
  }
  /**
   * 为保证最新，15秒请求一次故障接口
   */
  dealWithFaultInfo = () => {
    this.props.panelActions.fetchFaultInfo();
    this.faultTimer = setInterval(() => {
      this.props.panelActions.fetchFaultInfo();
    }, 15000);
  };

  /**
   * 监听滑动，改变导航栏模式
   */
  dealWithScrollListener = () => {
    this.scrollYListenerId = this.scrollY.addListener(({value}) => {
      const maxScrollDistance = 40;
      throttle(() => {
        if (value >= maxScrollDistance) {
          this.setState({
            headerOpacity: 1,
          });
        } else {
          const headerOpacity = value / maxScrollDistance;
          this.setState({
            headerOpacity,
          });
        }
      }, 1000)();
    });
  };

  /**
   * ota 原生点击完成按钮监听
   * 可以认为ota升级已经结束，消除小红点和强制升级
   */
  dealWithNativeOtaDone = () => {
    CommonEmitter.addListener('otaUpdateDidCompleted', () => {
      console.log('otaUpdateDidCompleted');
      this.fromOtaDone = true;

      this.props.panelActions.setOtaResult({
        showRed: false,
        isForceUpdate: false,
      });
    });
  };

  /**
   * 监听 原生->RN， RN->原生
   */
  dealWithRNContainerPushOrPop = () => {
    CommonEmitter.addListener('RNContainerViewWillAppear', () => {
      console.log('RNContainerViewWillAppear');
      this.props.panelActions.setIsInRNContainerVC(true);
      // 重新订阅一下 topic
      this.dealWithTopicSubscribe();
      // 检查强制升级弹框
      if (!this.fromOtaDone) {
        this.checkOtaForceAlert();
      }
    });
    CommonEmitter.addListener('RNContainerViewWillDisAppear', res => {
      console.log('RNContainerViewWillDisAppear');
      this.props.panelActions.setIsInRNContainerVC(false);
      this.fromOtaDone = false;
    });
  };

  /**
   * 安卓物理返回键处理
   */
  dealWithAndroidBack = () => {
    if (Platform.OS === 'android') {
      // console.log('dealWithAndroidBack');
      CommonEmitter.addListener('keyBackDown', res => {
        // console.log('keyBackDown---res--', res);
        if (LogUtils.currentPage === 'ViewPanelHome') {
          this.dealWithWillUnmount();
        }
        // console.log('keyBackDown---res--', res);
        this.androidGoBack();
      });
    }
  };

  /**
   * 1. 获取设备详情并更新设备状态
   * 2. 监听RN容器focus后，再次获取设备详情
   */
  dealWithFetchDeviceDetail = () => {
    CommonEmitter.addListener('NAVIGATOR_ON_WILL_FOCUS', () => {
      console.log('NAVIGATOR_ON_WILL_FOCUS');
      this.fetchDetailAndUpdateConnectStatus();
    });
    this.fetchDetailAndUpdateConnectStatus();
  };

  /**
   * check ota force alert
   */
  checkOtaForceAlert = () => {
    const {t} = this.props;
    this.props.panelActions.dealWithOtaForceAlert(
      t(geti18nText('home_otaAlertMsg_textview_text')),
    );
  };

  /**
   * 1. 获取设备详情
   * 2. 获取到设备详情之后，更新设备的状态
   * 3. 检查强制升级弹框
   */
  fetchDetailAndUpdateConnectStatus = () => {
    this.props.panelActions.fetchDeviceDetail().then(res => {
      const isConnected = Number(res.isOnline) === 1;
      this.props.panelActions.setWifiConnectStatus(isConnected);
      this.checkOtaForceAlert();
    });
  };

  /**
   * 处理 UTC 时钟校准
   */
  getUTCDate = () => {
    device.requestUTCDateResolveBlock?.(res => {
      // console.log('current utc date cmd hex:', res);
      const cmdHex = res;
      const {mac} = this.props.panel;
      device.sendCmd(cmdHex, mac);
    });
  };

  /**
   * @typedef {object} TopicResponse
   * @property {string} type
   * @property {string} payload
   * 处理topic变化
   */
  dealWithTopicChange = () => {
    CommonEmitter.addListener('topicDataDidChange', res => {
      try {
        /**
         * @type {TopicResponse}
         */
        let response = JSON.parse(res);
        const {type: topicType, payload} = response;
        console.warn('topicDataDidChange payload:', payload, topicType);

        // 连接状态topic 时间戳处理
        if (
          topicType.includes(topicMap.connect4G) ||
          topicType.includes(topicMap.disConnect4G)
        ) {
          const payloadObj = JSON.parse(payload);
          const {timestamp} = payloadObj;
          console.log('----timestamp---', timestamp);
          const timestampNum = Number(timestamp);
          // 过滤先发后到
          const lastTimestamp = this.props.panel.connectChangeTimestamp;
          if (lastTimestamp !== undefined && timestampNum < lastTimestamp) {
            return;
          }
          console.log('----timestampNum---', timestampNum);
          console.log('----lasttimestampNum---', lastTimestamp);
          // 记录当前值作为下次 topic回来时比较
          this.props.panelActions.setConnectChangeTimestamp(timestampNum);
        }

        if (topicType.includes(topicMap.connect4G)) {
          console.warn('Wifi connected', payload);
          this.props.panelActions.setWifiConnectStatus(true);
          return;
        }

        if (topicType.includes(topicMap.disConnect4G)) {
          console.warn('Wifi disconnected', payload);
          this.props.panelActions.setWifiConnectStatus(false);
          return;
        }

        if (
          topicType.includes(topicMap.shadowUpdateAcceptedSuffix) ||
          topicType.includes(topicMap.shadowGetAcceptedSuffix)
        ) {
          try {
            const payloadData = JSON.parse(payload);
            this.props.panelActions.parseThingModel(payloadData);
          } catch (e) {
            console.error('JSON.parse error:', e);
          }
          return;
        }
        console.warn('there is other topic', topicType);
      } catch (e) {
        console.error('topicDataDidChange exception: ', e);
      }
    });
  };

  /**
   * publish message to iot-core topic
   */
  dealWithTopicPublish = () => {
    const {deviceId} = this.props.panel;
    this.props.panelActions.publishTopic(deviceId);
  };

  /**
   * subscribe iot-core topic
   */
  dealWithTopicSubscribe = () => {
    const {deviceId} = this.props.panel;
    this.props.panelActions.subscribe(deviceId);
  };

  componentWillUnmount() {
    this.dealWithWillUnmount();
  }

  dealWithWillUnmount = () => {
    // 主设备取消订阅
    const {deviceId} = this.props.panel;
    this.props.panelActions.unsubscribe(deviceId);
    OtaUtils.unsubscribeToRelatedOtaTopics(deviceId);
    OtaUtils.removeRelatedEventListners();
    MessageUtils.removeMessageListener();

    // ota listener
    CommonEmitter.removeAllListeners('otaUpdateDidCompleted');

    // push notification
    CommonEmitter.removeAllListeners('didRecievePushNotification');

    // rn container
    CommonEmitter.removeAllListeners('NAVIGATOR_ON_WILL_FOCUS');
    CommonEmitter.removeAllListeners('RNContainerViewWillAppear');
    CommonEmitter.removeAllListeners('RNContainerViewWillDisAppear');

    this.scrollY.removeListener(this.scrollYListenerId);

    // android 物理返回键监听
    if (Platform.OS === 'android') {
      CommonEmitter.removeAllListeners('keyBackDown');
    }
    this.faultTimer && clearInterval(this.faultTimer);
  };

  /**
   * 1. 查询ota升级，
   * 2. 保存ota升级信息
   * 3. 检查强制升级
   */
  checkOtaUpdateAndForceAlert = () => {
    this.props.panelActions.checkAndSetOtaResult(() => {
      this.checkOtaForceAlert();
    });
  };

  /**
   * 处理页面返回
   */
  dealWithGoBack = () => {
    this.dealWithWillUnmount();
    mobile.back();
  };

  render() {
    const {
      deviceName,
      deviceOfflineDays,
      isWifiConnected,
      isOfflinePopupPoped,
    } = this.props.panel;
    const {headerMode, headerOpacity, navHeaderHeight} = this.state;

    // const isWifiConnected = true;
    const disabled = !isWifiConnected;
    const offlineIsAvailable =
      isWifiConnected === false && isOfflinePopupPoped === false;

    const {t} = this.props;

    return (
      <PageView>
        <View
          style={styles.navHeaderContainer}
          onLayout={({nativeEvent}) => {
            const {height} = nativeEvent.layout;
            this.setState({
              navHeaderHeight: height,
            });
          }}>
          <HeaderView
            style={[
              styles.navHeader,
              {
                backgroundColor: 'rgba(255, 255, 255, ' + headerOpacity + ')',
              },
            ]}
            mode={headerMode}
            elevated={false}
            onLeftPress={() => {
              this.dealWithGoBack();
            }}
            showMore
            onRightPress={() => {
              this.goPage('ViewDetailList');
            }}
            title={
              <TouchableOpacity
                style={styles.headerTitleContainer}
                onPress={() => {
                  this.goPage('ViewEditName', {
                    deviceName,
                  });
                }}>
                <Text
                  numberOfLines={1}
                  ellipsizeMode="tail"
                  style={styles.headerDeviceName}>
                  {deviceName}
                </Text>
                {isWifiConnected ? null : (
                  <Text
                    numberOfLines={1}
                    ellipsizeMode="tail"
                    style={styles.headerConnectStatus}>
                    {t(geti18nText('home_wifiNotConnected_textview_text'))}
                  </Text>
                )}
              </TouchableOpacity>
            }
          />
        </View>
        <Animated.ScrollView
          contentContainerStyle={styles.scrollContainer}
          scrollEventThrottle={1}
          onScroll={Animated.event(
            [{nativeEvent: {contentOffset: {y: this.scrollY}}}],
            {useNativeDriver: true},
          )}>
          {/* 顶部状态，包括电池包电量/充电状态/剩余充电时长和 各种状态的电池包数量 */}
          <TopStatusView
            navHeaderHeight={navHeaderHeight}
            disabled={!isWifiConnected}
          />

          {/* 设备事件消息 */}
          <Notification />

          {/* ota升级横幅 */}
          <UpdateItem connected={isWifiConnected} style={{marginBottom: 30}} />

          {/* 充电总览，各种状态的电池数量，三头和dc充电器的数量 */}
          <ChargingOverview disabled={disabled} />

          {/* 设置开关，包括设置预约充电，充电完成提醒，dc-dc充电优先，电池充电优化 */}
          <FunctionSetting disabled={disabled} />

          <WhiteSpace size={isIphoneX ? 34 : 0} />
        </Animated.ScrollView>

        {/* 离线弹窗 */}
        <OfflinePopUp
          isAvailable={offlineIsAvailable}
          deviceName={deviceName}
          offlineDays={deviceOfflineDays}
          panel={this.props.panel}
          onPressMore={() => {
            this.props.panelActions.setIsOfflinePopupPoped(true);
          }}
        />
      </PageView>
    );
  }
}

export default withTranslation('all')(Home);

const styles = CustomStyleSheet.create({
  navHeaderContainer: {
    position: 'absolute',
    zIndex: 1,
  },
  navHeader: {
    borderBottomWidth: 0,
  },
  scrollContainer: {
    // paddingVertical: 11,
  },
  headerTitleContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    height: '100%',
  },
  headerDeviceName: {
    fontSize: 22,
    lineHeight: 28,
    color: '#000',
    fontWeight: '500',
  },
  headerConnectStatus: {
    fontSize: 16,
    color: '#000',
    lineHeight: 28,
  },
});
