/*
 * @Author: 邢立伟 <EMAIL>
 * @Date: 2023-09-27 10:14:59
 * @LastEditors: 邢立伟 <EMAIL>
 * @LastEditTime: 2024-07-17 15:07:51
 * @FilePath: /61004/src/pages/panel/components/NotifyView
 * @Description: 首页通知条
 */
import React, {memo} from 'react';
import {observer} from 'mobx-react';
import {StyleSheet, ViewStyle} from 'react-native';
import {Carousel} from '@ant-design/react-native';
import {Utils} from 'cvn-panel-kit';
import {tracer, eleIdMap} from '@tracer';
import {ErrorView} from './ErrorView';
import DeviceConnectionStore from 'IOTRN/src/mobx/deviceConnectionStore';
import DeviceAbnormalStore from 'IOTRN/src/mobx/deviceAbnormalStore';
import DeviceStore from 'IOTRN/src/mobx/deviceStore';
const {JumpUtils} = Utils;
/**
 * 消息告警条
 * @param {Object} store - 注入的store对象
 * @param {Object} style - 自定义样式
 * @param {Array} homeMsgList - 主页消息列表
 * @param {Object} panel - 面板数据
 * @param {Object} home - 主页数据
 * @returns {Node} - NotifyView组件
 */

interface InjectedStores {
  style: ViewStyle;
  deviceConnectionStore: DeviceConnectionStore;
  deviceAbnormalStore: DeviceAbnormalStore;
  deviceStore: DeviceStore;
}
const NotifyView = observer(
  ({
    style = {},
    deviceConnectionStore,
    deviceAbnormalStore,
    deviceStore,
  }: InjectedStores) => {
    const {homeMsgList} = deviceAbnormalStore.state;
    if (homeMsgList.length === 0 || deviceConnectionStore.actions.allDisabled) {
      return null;
    }
    return (
      <Carousel
        dots={false}
        style={styles.wrapper}
        selectedIndex={0}
        autoplay
        autoplayInterval={5000}
        infinite
        vertical>
        {homeMsgList.map(item => {
          // eventType: info, warning, error
          const {eventType, faultTitle} = item;
          let errorSource = require('@assets/msg/61004_msg_warning.png');
          switch (eventType) {
            case 'info':
              errorSource = require('@assets/msg/61004_msg_info.png');
              break;
            case 'warning':
              errorSource = require('@assets/msg/61004_msg_warning.png');
              break;
            // 兼容后台数据返回不标准
            case 'warn':
              errorSource = require('@assets/msg/61004_msg_warning.png');
              break;
            case 'error':
              errorSource = require('@assets/msg/61004_msg_error.png');
              break;
            default:
              break;
          }
          return (
            <ErrorView
              key={item.faulttitle + item.eventType}
              isShow
              text={faultTitle}
              style={[styles.errorView, style]}
              source={errorSource}
              onPress={() => {
                // 找到指定对象，传参跳转
                tracer.click({eleid: eleIdMap.Fault_Message_Button_Click});
                const {
                  uuid,
                  faultMessageId, // 首页消息接口返回
                  deviceId = deviceStore.state.initialParams.deviceId, // 如果取不到，取panel的deviceId
                  productId,
                  createTime,
                } = item;
                const route = 'ChervonIot://EGO/MessageCenter/MessageDetail';
                const params = {
                  messageType: 2, // 调取消息详情，固定为2 或 '2'?
                  uuid,
                  systemMessageId: faultMessageId,
                  deviceId,
                  productId,
                  createTime,
                };
                /**
                 * 消息详情原消息详情接口：/get/message/detail  区别：增加 systemMessageId字段，
                 * 云端取值自己判断取uuid还是systemMessageId
                 */
                const tmpStr = JSON.stringify(params);
                const tParams = JSON.parse(tmpStr);
                JumpUtils.jumpTo(route, tParams);
              }}
            />
          );
        })}
      </Carousel>
    );
  },
);

export default memo(NotifyView);

const styles = StyleSheet.create({
  wrapper: {
    width: '100%',
    height: 50,
  },
  errorView: {
    marginBottom: 10,
    height: 48,
  },
});
