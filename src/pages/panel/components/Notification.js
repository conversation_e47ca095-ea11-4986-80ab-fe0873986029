/*
 * @Author: 邢立伟 <EMAIL>
 * @Date: 2023-02-09 11:35:30
 * @LastEditors: 邢立伟 <EMAIL>
 * @LastEditTime: 2024-12-02 11:35:43
 * @FilePath: /react-native-panels/src/pages/panel/components/Notification.js
 * @Description: 故障消息展示
 */
import {inject} from 'mobx-react';
import {CVNIcon} from '@cvn/rn-panel-kit';
import React from 'react';
import {StyleSheet, View, Text} from 'react-native';
import PropTypes from 'prop-types';
import RenderHtml from 'react-native-render-html';
import {geti18nText} from '@i18n/config';
import {useTranslation} from 'react-i18next';
import {DEVICE_WIDTH} from 'IOTRN/src/utils/device';
import {parse} from 'html-parse-string';

/**
 * 首页消息告警view
 * @param {Object} style - 自定义样式
 * @param {Object} faultInfo - 故障信息
 * @returns {Node} - SlopeAlarm组件
 */
export const Notification = inject(store => ({
  faultInfo: store.panel.faultInfo,
  isWifiConnected: store.panel.isWifiConnected,
}))(({faultInfo = {}, style = {}, isWifiConnected = false}) => {
  const {errorCode, faultMessageTitle, suggestionContent} = faultInfo;
  if (!isWifiConnected || !(errorCode || faultMessageTitle)) {
    return null;
  }
  const {t} = useTranslation('all');
  const errorCodePrefix = `${t(geti18nText('home_errorcode_textview_text'))}`;
  const errorCodeText = `${errorCodePrefix} ${errorCode}`;
  const errorTitle = faultMessageTitle;
  const errorImgBase64 =
    'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACwAAAAsCAMAAAApWqozAAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAAJcEhZcwAACxMAAAsTAQCanBgAAACHUExURUdwTNozLNozK98wMNoyK984MNcwKNs0LNsyK9kyLNozK9oyK9gyK9oyLd9AMN8wINkyKtoyK9syKtkxK9syLNsyLNkzLdo1K9wzLd0zLNsyK9gyKdoyKtoyLNo0K9syK9oyK981K9owK9oyK////+yYlf3y8uNlYOVybeVybuuMh+2ZleqLiPoxVs0AAAAjdFJOUwC/vxDfICBAcICfYHBgEBCAn3+gf+9QMFBvj3CQkI/v7zAwmq3emAAAARxJREFUOMullWubgiAQhVEMQTOr7bq32p20tvr/v28hn9yEQab1fMT3wTMchmFskCbrQiolV2mYlPES7orzUQ+axmAp8+GTV0CE42kCqBLE/Ba8mtvsAoBM97IAbx2/ENCD75FV27muL1aVvIWz7pfTQevYXftsN7b+WRm48hjJKHCMb4zD0LjOafD4Bkc0uPEBNHjP0UBwGL41vKHChYYlFTYVKiqsnoVdG0cD/6A2CvdCVnV9dVdXGv4CosxV4jsijMeNq4lb0uD8BnNn3W0rLcFQH1hbwczT22go4t6EH+FQZm138yQUSiL+98gEj2/afezUE2wvPR72mDMmXjD2vfRMFReP+gacyP+yX0ayDM7CdGOGZrEuhw3fX3yss32CXNS8AAAAAElFTkSuQmCC';
  // 创建一个新的 span 元素 'Suggestion: '
  const suggesttionPrefix = t(geti18nText('home_suggestion_textview_text'));
  const suggestionHtml = `<span style="color: #DA322B; background-color: rgb(255, 255, 255); font-size: 15px;">${suggesttionPrefix}: </span>`;
  const isPureText = !containsHtmlTags(suggestionContent);

  let updatedRichText;
  if (isPureText) {
    const formatedSuggestionHtml = `<span style="color: #DA322B; background-color: rgb(255, 255, 255); font-size: 15px;">${suggestionContent}</span>`;
    updatedRichText = `${suggestionHtml}${formatedSuggestionHtml}`;
  } else {
    updatedRichText = suggestionContent?.replace(
      /<p([^>]*)>/,
      (_, group1) => `<p${group1}>${suggestionHtml}`,
    );
  }
  const cStyle = isPureText ? {} : {marginTop: -12};
  return (
    <View style={[styles.container, style]}>
      <CVNIcon
        source={{
          uri: errorImgBase64,
        }}
      />
      <View style={styles.rightBox}>
        <Text style={styles.errorCode}>{errorCodeText}</Text>
        <Text style={styles.erroTitle}>{errorTitle}</Text>
        {suggestionContent ? (
          <HtmlText style={cStyle} htmlContent={updatedRichText} />
        ) : null}
      </View>
    </View>
  );
});
Notification.propTypes = {
  panel: PropTypes.object,
  style: PropTypes.object,
  panelActions: PropTypes.object,
  isWifiConnected: PropTypes.bool,
};
/**
 * HtmlText组件
 * 用于渲染HTML格式的文本
 *
 * @param {Object} style - 自定义样式
 * @param {string} htmlContent - 要渲染的HTML内容
 * @returns {Node} - 渲染的HTML内容
 */
const HtmlText = ({style = {}, htmlContent}) => {
  const source = {
    html: htmlContent,
  };
  return (
    <View style={[styles.htmlContainer, style]}>
      <RenderHtml contentWidth={DEVICE_WIDTH - 105} source={source} />
    </View>
  );
};
HtmlText.propTypes = {
  style: PropTypes.object,
  htmlContent: PropTypes.string.isRequired, // Assuming htmlContent is required
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    marginHorizontal: 25,
    marginBottom: 30,
    padding: 15,
    backgroundColor: '#FFFFFF',
    borderRadius: 15,
    shadowColor: '#000000',
    shadowOffset: {
      width: 0,
      height: 6,
    },
    shadowRadius: 15,
    shadowOpacity: 0.05,
  },
  rightBox: {
    marginLeft: 14,
    marginRight: 14,
  },
  errorCode: {
    color: '#DA322B',
    fontSize: 15,
    fontWeight: '500',
  },
  erroTitle: {
    color: '#DA322B',
    fontSize: 15,
    fontWeight: '500',
  },
  suggestionText: {
    color: '#DA322B',
    fontSize: 15,
    fontWeight: '500',
  },
  htmlContainer: {
    flex: 1,
  },
});

export default Notification;

/**
 * @description: 是否包含html标签
 * @param {String} str
 * @returns {Boolean}
 */
export const containsHtmlTags = str => {
  const parsed = parse(str);
  return parsed.some(tag => tag.type === 'tag'); // 判断是否存在 HTML 标签
};
