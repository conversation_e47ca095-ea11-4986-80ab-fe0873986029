import React, {Fragment, useMemo} from 'react';
import {View, Text, StyleSheet} from 'react-native';
import {useTranslation} from 'react-i18next';
import PropTypes from 'prop-types';
import {inject} from 'mobx-react';
import {
  ChargingStatusIcon,
  ReadyStatusIcon,
  StandbyStatusIcon,
  EmptyStatusIcon,
  DcChargerIcon,
  ThreePortAdaptorIcon,
} from '../Icons';
import StatusBatteryItem from './StatusBatteryItem';
import DeviceCount from './DeviceCount';
import {Divider} from 'react-native-elements';
import {geti18nText} from '@i18n/config';

const ChargingOverview = inject(store => ({
  readyBatterysPower: store.panel.home.readyBatterysPower,

  readyBatterysCount: store.panel.home.readyBatterysCount,
  chargingBatterysCount: store.panel.home.chargingBatterysCount,
  stanbyBatterysCount: store.panel.home.stanbyBatterysCount,
  emptyBatterysCount: store.panel.home.emptyBatterysCount,

  threePortAdaptorCount: store.panel.home.threePortAdaptorCount,
  dcDcChargerCount: store.panel.home.dcDcChargerCount,
}))(
  ({
    readyBatterysPower,
    readyBatterysCount,
    chargingBatterysCount,
    stanbyBatterysCount,
    emptyBatterysCount,
    threePortAdaptorCount,
    dcDcChargerCount,
    disabled = true,
  }) => {
    const {t} = useTranslation('all');
    // console.log('overview render');

    const statusBatteryList = [
      {
        key: 'ready',
        status: t(geti18nText('home_batteryReady_textview_text')),
        num: readyBatterysCount,
        unit: 'batteries',
        mark: <ReadyStatusIcon />,
        extraValue: readyBatterysPower,
        showExtraValue: true,
      },
      {
        key: 'charging',
        status: t(geti18nText('home_batteryCharging_textview_text')),
        num: chargingBatterysCount,
        unit: 'batteries',
        mark: <ChargingStatusIcon />,
      },
      {
        key: 'standby',
        status: t(geti18nText('home_batteryStandby_textview_text')),
        num: stanbyBatterysCount,
        unit: 'batteries',
        mark: <StandbyStatusIcon />,
      },
      {
        key: 'empty',
        status: t(geti18nText('home_batteryEmpty_textview_text')),
        num: emptyBatterysCount,
        unit: 'slots',
        mark: <EmptyStatusIcon />,
      },
    ];

    const adaptorAndChargerList = useMemo(() => {
      return [
        {
          key: 'adaptor',
          num: threePortAdaptorCount,
          unit: 'X',
          icon: <ThreePortAdaptorIcon />,
        },
        {
          key: 'charger',
          num: dcDcChargerCount,
          unit: 'X',
          icon: <DcChargerIcon />,
        },
      ];
    }, [threePortAdaptorCount, dcDcChargerCount]);

    return (
      <View style={styles.container}>
        <Text style={styles.title}>
          {t(geti18nText('home_chargingOverview_title_textview_text'))}
        </Text>
        <View style={disabled && styles.disabled}>
          <View style={styles.batteryListContainer}>
            {statusBatteryList.map(item => {
              return (
                <StatusBatteryItem
                  key={item.key}
                  {...item}
                  disabled={disabled}
                />
              );
            })}
          </View>
          <View style={styles.deviceContainer}>
            {adaptorAndChargerList.map((item, index) => {
              return (
                <Fragment key={item.key}>
                  <DeviceCount {...item} disabled={disabled} />
                  {index === 0 && <Divider orientation="vertical" />}
                </Fragment>
              );
            })}
          </View>
        </View>
      </View>
    );
  },
);

ChargingOverview.propTypes = {
  readyBatterysPower: PropTypes.number,
  readyBatterysCount: PropTypes.number,
  chargingBatterysCount: PropTypes.number,
  stanbyBatterysCount: PropTypes.number,
  emptyBatterysCount: PropTypes.number,
  threePortAdaptorCount: PropTypes.number,
  dcDcChargerCount: PropTypes.number,
  disabled: PropTypes.bool,
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 25,
    marginBottom: 20,
  },
  disabled: {
    opacity: 0.4,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#3C3936',
    marginBottom: 18,
  },
  batteryListContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flexWrap: 'wrap',
  },
  deviceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#ffffff',
    borderRadius: 10,
    paddingVertical: 25,
  },
});

export default ChargingOverview;
