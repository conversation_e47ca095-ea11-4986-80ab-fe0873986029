import React from 'react';
import {View, Text, StyleSheet} from 'react-native';

const DeviceCount = ({
  num = 0,
  unit = 'X',
  icon = null,
  disabled = true,
  ...props
}) => {
  const blankText = '--';
  const isValidValue = value => value >= 0;
  return (
    <View
      style={{
        flexDirection: 'row',
        alignItems: 'center',
        width: '50%',
        paddingLeft: 16,
      }}
      {...props}>
      <Text style={{marginRight: 10}}>
        {disabled || !isValidValue(num) ? (
          <Text style={styles.num}>{blankText}</Text>
        ) : (
          <>
            <Text style={styles.num}>{num}</Text>
            <Text style={styles.unit}>{unit}</Text>
          </>
        )}
      </Text>
      {icon}
    </View>
  );
};

const styles = StyleSheet.create({
  num: {
    fontSize: 27,
    // lineHeight: 32,
    // letterSpacing: 2,
    fontWeight: '500',
    color: '#000000',
  },
  unit: {
    fontSize: 20,
    // lineHeight: 32,
    // letterSpacing: 2,
    fontWeight: '500',
    color: '#000000',
  },
});

export default DeviceCount;
