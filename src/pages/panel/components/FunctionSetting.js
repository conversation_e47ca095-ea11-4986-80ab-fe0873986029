import React, {useState, useEffect, Fragment, isValidElement} from 'react';
import {inject} from 'mobx-react';
import {View, Text} from 'react-native';
import {map} from 'lodash-es';
import PropTypes from 'prop-types';
import {Divider, Switch} from 'react-native-elements';
import CustomStyleSheet from '@utils/style/index.js';
import {useTranslation} from 'react-i18next';
import {map as ModelMap, issueOnlyMap} from '@config/model.js';
import {Card, Cell} from '@components';
import {mobile} from '@cvn/rn-panel-kit';
import TimePicker from '@components/TimePicker';
import dayjs from 'dayjs';
import {timeStrToTimestamp, convertTo24HourFormat} from '@utils/time.js';
import {getNextHalfHourTimeStr, convertTo12HourFormat} from '@utils/time';
import {geti18nText} from '@i18n/config';

const FunctionSetting = inject(store => ({
  deviceId: store.panel.deviceId,
  appSettingOfHour: store.panel.appSettingOfHour,
  chargingProgress: store.panel.home.chargingProgress,
  scheduleChargingSwitch: store.panel.setting.scheduleChargingSwitch,
  completionReminderSwitch: store.panel.setting.completionReminderSwitch,
  dcDcPrioritySwitch: store.panel.setting.dcDcPrioritySwitch,
  optimizedBatteryChargingSwitch:
    store.panel.setting.optimizedBatteryChargingSwitch,
  chargingScheduleStartTime: store.panel.setting.chargingScheduleStartTime,
  completionReminderValue: store.panel.setting.completionReminderValue,
  totalBatterysCount: store.panel.home.totalBatterysCount,
  panelActions: store.panelActions,
}))(
  ({
    deviceId,
    appSettingOfHour,
    chargingProgress,
    scheduleChargingSwitch,
    completionReminderSwitch,
    dcDcPrioritySwitch,
    optimizedBatteryChargingSwitch,
    chargingScheduleStartTime,
    completionReminderValue,
    totalBatterysCount,
    panelActions,
    disabled = true,
  }) => {
    const {t} = useTranslation('all');
    const [showPicker, setShowPicker] = useState(false);
    const [pickerTime, setPickerTime] = useState(chargingScheduleStartTime);
    const is12HourFormat = appSettingOfHour === '12hours';

    const list = [
      {
        key: 'schedule_charging',
        title: t(geti18nText('home_scheduleChargingTitle_textview_text')),
        leftIconSource: require('@assets/common_icon_scheduleCharging.png'),
        showRight: true,
        disabled: disabled,
        rightElement: (
          <Switch
            color="#77BC1F"
            disabled={disabled}
            value={scheduleChargingSwitch}
            onValueChange={flag => {
              // 打开等设置时间后再集中多个物模型一起下发，关闭直接下发关闭指令
              if (flag) {
                setShowPicker(true);
                const nextHalfHourTimeStr = getNextHalfHourTimeStr();
                setPickerTime(
                  is12HourFormat
                    ? convertTo12HourFormat(nextHalfHourTimeStr)
                    : nextHalfHourTimeStr,
                );
              } else {
                panelActions.editProperty({
                  deviceId,
                  propertyData: {
                    [ModelMap.schedule_charging_switch]: false,
                  },
                });
              }
            }}
          />
        ),
        showContent: scheduleChargingSwitch,
        content: (
          <View style={styles.contentContainer}>
            <Divider />
            <Cell
              iconShow={false}
              containerStyle={{marginTop: 9}}
              itemStyle={styles.scheduleChargingStartsFrom}
              title={`${t(
                geti18nText('home_scheduleChargingStartsFrom_textview_text'),
              )} ${chargingScheduleStartTime}`}
              titleStyle={styles.scheduleChargingText}
              onPress={() => {
                if (disabled) {
                  return;
                }
                setShowPicker(true);
              }}
            />
          </View>
        ),
      },
      {
        key: 'priority_charging',
        title: t(geti18nText('home_dcPriorityCharging_textview_text')),
        description: t(
          geti18nText('home_dcPriorityChargingDesc_textview_text'),
        ),
        leftIconSource: require('@assets/common_icon_priorityCharging.png'),
        showRight: true,
        disabled: disabled,
        rightElement: (
          <Switch
            color="#77BC1F"
            disabled={disabled}
            value={dcDcPrioritySwitch}
            onValueChange={flag => {
              panelActions.editProperty({
                deviceId,
                propertyData: {
                  [ModelMap.dc_dc_priority_charging_switch]: flag,
                },
              });
            }}
          />
        ),
      },
      {
        key: 'optimized_battery_charging',
        title: t(geti18nText('home_optimizedBatteryCharging_textview_text')),
        description: t(
          geti18nText('home_optimizedBatteryChargingDesc_textview_text'),
        ),
        leftIconSource: require('@assets/common_icon_batteryOptimization.png'),
        showRight: true,
        disabled: disabled,
        rightElement: (
          <Switch
            color="#77BC1F"
            disabled={disabled}
            value={optimizedBatteryChargingSwitch}
            onValueChange={flag => {
              panelActions.editProperty({
                deviceId,
                propertyData: {
                  [ModelMap.optimized_battery_charging_switch]: flag,
                },
              });
            }}
          />
        ),
      },
    ];

    useEffect(() => {
      setPickerTime(chargingScheduleStartTime);
    }, [chargingScheduleStartTime]);

    return (
      <View style={styles.container}>
        <Text style={styles.title}>
          {t(geti18nText('home_functionSetting_textview_text'))}
        </Text>
        {map(list, item => {
          return (
            <Fragment key={item.key}>
              <Card
                key={item.key}
                title={item.title}
                style={{
                  marginHorizontal: 0,
                  paddingVertical: 13,
                  borderRadius: 10,
                }}
                icon={item.leftIconSource}
                disabled={item.disabled}
                rightElement={item.rightElement}>
                {item.description ? (
                  <Text
                    style={[
                      styles.descText,
                      {
                        marginTop: 8,
                      },
                    ]}>
                    {item.description}
                  </Text>
                ) : null}
                {item.content && isValidElement(item.content) ? (
                  <View style={{display: item.showContent ? 'flex' : 'none'}}>
                    {item.content}
                  </View>
                ) : null}
              </Card>
              {item.bottomDescription ? (
                <Text
                  style={[
                    styles.descText,
                    {
                      paddingHorizontal: 15,
                      marginBottom: 16.5,
                      marginTop: -1,
                    },
                  ]}>
                  {item.bottomDescription}
                </Text>
              ) : null}
            </Fragment>
          );
        })}

        {showPicker && (
          <TimePicker
            closeSource={require('@assets/common_close.png')}
            visible={true}
            value={pickerTime}
            is12HourFormat={is12HourFormat}
            title={t(geti18nText('home_timePickerStartTime_textview_text'))}
            onClose={() => {
              panelActions.editProperty({
                deviceId,
                propertyData: {
                  [ModelMap.schedule_charging_switch]: false,
                },
              });
              setShowPicker(false);
            }}
            onSelect={value => {
              const currentDate = dayjs();
              const currentTimestamp = currentDate.unix();
              const selectedTimestamp = timeStrToTimestamp(
                is12HourFormat ? convertTo24HourFormat(value) : value,
              );
              if (selectedTimestamp < currentTimestamp) {
                mobile.toast(
                  t(geti18nText('home_timeToastTip_textview_text')),
                  () => {},
                );
                return;
              }
              panelActions.editProperty({
                deviceId,
                propertyData: {
                  [ModelMap.schedule_charging_switch]: true,
                  [issueOnlyMap.schedule_charging_now_time]: currentTimestamp,
                  [ModelMap.schedule_charging_start_time]: selectedTimestamp,
                },
              });
              setShowPicker(false);
            }}
            onChangeValue={() => {}}
          />
        )}
      </View>
    );
  },
);

FunctionSetting.propTypes = {
  deviceId: PropTypes.string,
  appSettingOfHour: PropTypes.string,
  scheduleChargingSwitch: PropTypes.bool,
  completionReminderSwitch: PropTypes.bool,
  dcDcPrioritySwitch: PropTypes.bool,
  optimizedBatteryChargingSwitch: PropTypes.bool,
  chargingScheduleStartTime: PropTypes.string,
  completionReminderValue: PropTypes.number,
  panelActions: PropTypes.object,
  disabled: PropTypes.bool,
};

const styles = CustomStyleSheet.create({
  container: {
    paddingHorizontal: 24,
    marginBottom: 20,
  },
  title: {
    fontSize: 16,
    lineHeight: 19,
    fontWeight: '500',
    color: '#000000',
    marginBottom: 18,
  },
  extra: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  scheduleChargingStartsFrom: {
    paddingLeft: 44,
    paddingRight: 15,
    paddingVertical: 6,
  },
  scheduleChargingText: {
    fontSize: 17,
    lineHeight: 20.5,
    fontWeight: '500',
    color: '#77BC1F',
  },
  sensitivityContentView: {
    paddingHorizontal: 5,
  },
  descText: {
    fontSize: 12,
    lineHeight: 14.5,
    color: '#999999',
    padding: 5,
  },
  sensitivityTextView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingTop: 12,
    paddingBottom: 8,
  },
  radioWrapView: {
    marginTop: 20,
    marginBottom: 5,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-around',
    // paddingHorizontal: 10
  },
  contentContainer: {
    marginTop: 13,
  },
});

export default FunctionSetting;
