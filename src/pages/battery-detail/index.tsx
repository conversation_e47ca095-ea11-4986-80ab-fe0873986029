import React from 'react';
import {StyleSheet, View, ScrollView} from 'react-native';
import {inject, observer} from 'mobx-react';
import Strings from '@i18n';
import {CVNIcon, PageView} from 'cvn-panel-kit';
import {HeaderView} from '@components';
import {DEVICE_WIDTH} from '@utils/device';
import {BatteryIconView} from '@pages/battery-detail/components';
import {goBack} from 'IOTRN/src/utils/pageAction';
import {InjectStore} from 'IOTRN/src/utils/interface';
import {BatteryDetailInfo} from '@types';

/** 背景图片大小是1500X2226，适配需要等比缩放
 * 基准屏宽度751
 * 两边距离为158
 * 总边距为316
 * 等比为0.42
 * 基准屏图片高度为1113
 * 距离头部距离为207
 */

interface BatteryViewProps {
  data?: BatteryDetailInfo[];
}

export const BatteryView = observer(({data = []}: BatteryViewProps) => {
  const TOTALPADDING = 0.42 * DEVICE_WIDTH; // 两边的总边距
  const IMGHEIGHT = DEVICE_WIDTH * 1.484; // 背景图片的高度
  const BATTERYWIDTH = (DEVICE_WIDTH - TOTALPADDING - 14) / 2;
  return (
    <CVNIcon
      style={{width: DEVICE_WIDTH, height: IMGHEIGHT}}
      resizeMode="stretch"
      source={require('@assets/battery/61004_icon_bg_battery_background.png')}>
      <View style={styles.container}>
        <View
          style={[
            styles.horizontalView,
            {
              width: DEVICE_WIDTH - TOTALPADDING,
              marginTop: (IMGHEIGHT * 207) / 1113,
            },
          ]}>
          {data.map((item: BatteryDetailInfo, index: number) => {
            return (
              <BatteryIconView
                key={`first${index}${index}`}
                style={{
                  width: BATTERYWIDTH,
                  height: BATTERYWIDTH * 0.64,
                  marginBottom: (IMGHEIGHT * 62) / 1113,
                }}
                item={item}
              />
            );
          })}
        </View>
      </View>
    </CVNIcon>
  );
});

/** 电池详情 */

const BatteryDetail = inject('rootStore')(
  observer(({rootStore, navigation}: InjectStore) => {
    return (
      <PageView>
        <HeaderView
          title={Strings.getLang('rn_61004_batteryinfo_title_textview_text')}
          onLeftPress={() => {
            goBack(navigation);
          }}
        />
        <ScrollView contentContainerStyle={styles.scrollView}>
          <View style={styles.batteryItem}>
            <BatteryView
              data={rootStore.batteryManagementStore.actions.batteryDetail.data}
            />
          </View>
        </ScrollView>
      </PageView>
    );
  }),
);
export default BatteryDetail;

const styles = StyleSheet.create({
  scrollView: {
    flex: 1,
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  container: {
    flex: 1,
    alignItems: 'center',
  },
  horizontalView: {
    flexDirection: 'row',
    justifyContent: 'center',
    flexWrap: 'wrap',
    gap: 7,
  },
  batteryItem: {
    marginTop: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
});
