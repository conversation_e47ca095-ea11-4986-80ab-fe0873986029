/*
 * @Author: 邢立伟 <EMAIL>
 * @Date: 2023-09-25 17:44:24
 * @LastEditors: 邢立伟 <EMAIL>
 * @LastEditTime: 2024-09-10 19:05:42
 * @FilePath: /61004/src/pages/usage-history/index
 * @Description: 上一次使用数据
 */
import React, {useEffect, useRef, useState} from 'react';
import dayjs from 'dayjs';
import {
  Text,
  View,
  ScrollView,
  StyleSheet,
  TouchableWithoutFeedback,
  StatusBar,
  TouchableOpacity,
  Platform,
} from 'react-native';
import {inject, observer} from 'mobx-react';
import {WhiteSpace, PageView, CVNIcon, mobile} from 'cvn-panel-kit';
import DeviceInfo from 'react-native-device-info';
import {HeaderView, BaseLineChart} from '@components';
import {isIphoneX} from '@utils/device';
import Strings from '@i18n';
import {tracer, eleIdMap} from 'IOTRN/src/utils/tracer';
import DateTypeSegment from './components/DateTypeSegment';
import Dropdown from './components/Dropdown';
import {isAfterToday} from 'IOTRN/src/utils/time';
import {BUSTYPEDEFAULTVALUE} from '@utils/constant';
import {useModal} from '@components/Modal';
import {RootStore} from 'IOTRN/src/mobx/rootStore';
import {goBack} from 'IOTRN/src/utils/pageAction';
import {usageHistory} from 'IOTRN/src/api';
import {EntryProps} from 'IOTRN/src/mobx/utils/interfaceProps';

const busTypes = {
  dateValue: 1, // 日
  weekValue: 2, // 周
  monthValue: 3, // 月
};

const datePeriods = {
  dateDefaultValue: 7,
  weekDefaultValue: 4,
  monthDefaultValue: 4,
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
function withCounter(WrappedComponent: any) {
  return function Modal(props: React.JSX.IntrinsicAttributes) {
    const {showModal} = useModal();
    return <WrappedComponent showModal={showModal} {...props} />;
  };
}

const filterData = [
  {
    title: Strings.getLang(
      'rn_61004_usagehistory_alertmowingtime_textview_text',
    ),
    index: 0,
  },
  {
    title: Strings.getLang(
      'rn_61004_usagehistory_alertmowingarea_textview_text',
    ),
    index: 1,
  },
  {
    title: Strings.getLang(
      'rn_61004_usagehistory_alertpowerconsumption_textview_text',
    ),
    index: 2,
  },
  {
    title: Strings.getLang(
      'rn_61004_usagehistory_alertdrivingdistance_textview_text',
    ),
    index: 3,
  },
  {
    title: Strings.getLang(
      'rn_61004_usagehistory_alertco2reduction_textview_text',
    ),
    index: 4,
  },
];
const subTitleData = [
  {
    title: Strings.getLang(
      'rn_61004_usagehistory_alerttotaltime_textview_text',
    ),
    index: 0,
  },
  {
    title: Strings.getLang(
      'rn_61004_usagehistory_alerttotalmowingarea_textview_text',
    ),
    index: 1,
  },
  {
    title: Strings.getLang(
      'rn_61004_usagehistory_alerttotalpowerconsumption_textview_text',
    ),
    index: 2,
  },
  {
    title: Strings.getLang(
      'rn_61004_usagehistory_alerttotaldrivingdistance_textview_text',
    ),
    index: 3,
  },
  {
    title: Strings.getLang(
      'rn_61004_usagehistory_alerttotalco2reductione_textview_text',
    ),
    index: 4,
  },
];

const FORMATDATE = 'dddd, MMM DD YYYY';

interface InjectStore {
  rootStore: RootStore;
  navigation: {[key: string]: Function};
  showModal: (props: {
    onConfirm: () => void;
    contentRender?: React.ReactNode;
  }) => void;
}

const MyClassComponent = inject('rootStore')(
  observer(({rootStore, navigation, showModal}: InjectStore) => {
    const dateStrRef = useRef<string>(dayjs().format('YYYY/MM/DD'));
    const [modalVisible, setModalVisible] = React.useState(false);
    const selectedIndexRef = useRef<number>(0);
    const [headerHeight, setHeaderHeight] = React.useState(0);
    const [selectedTitle, setSelectedTitle] = React.useState(
      Strings.getLang('rn_61004_usagehistory_alertmowingtime_textview_text'),
    );
    const [selectedSubTitle, setSelectedSubTitle] = React.useState(
      Strings.getLang('rn_61004_usagehistory_alerttotaltime_textview_text'),
    );

    const [valueText, setValueText] = React.useState(
      rootStore.deviceChartStore.state.totalMowingTime,
    );

    const [showDateStr, setShowDateStr] = useState(
      dayjs(Date.now()).format(FORMATDATE),
    );

    const dateTypeRef = useRef<number>(1);
    const lastDateTypeRef = useRef<number>(1);

    useEffect(() => {
      rootStore.deviceChartStore.actions.setSelectedUnit(
        Strings.getLang('rn_61004_panelhome_hour_textview_text'),
      );
      getUsageHistory();
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    /**
     * @description: 获取日期
     * 日场景入参: "2024/01/05"
     * 周场景入参： "2024/01/05-2024/01/10"
     * 月场景入参： "2024/01"
     */
    const getDateValue = (date = '') => {
      const dateString = dayjs(date).format('YYYY/MM/DD');
      return dateString;
    };
    /**
     * @description: 获取数据类型
     */
    const getBusType = () => {
      let result = [''];
      switch (selectedIndexRef.current) {
        case 0:
          result = BUSTYPEDEFAULTVALUE.mowingTime;
          break;
        case 1:
          result = BUSTYPEDEFAULTVALUE.cuttingArea;
          break;
        case 2:
          result = BUSTYPEDEFAULTVALUE.powerConsumption;
          break;
        case 3:
          result = BUSTYPEDEFAULTVALUE.drivingDistance;
          break;
        case 4:
          result = BUSTYPEDEFAULTVALUE.c02Reduction;
          break;
        default:
          break;
      }
      return result;
    };

    /**
     * @description: 历史数据接口
     * @param {String} deviceId
     */
    const getUsageHistory = () => {
      // 首页默认是日维度
      const dateValue = getDateValue(dateStrRef.current);
      const busType = getBusType();
      const deviceId = rootStore.deviceStore.state.initialParams.deviceId;

      let datePeriod = datePeriods.monthDefaultValue;
      // 当dateType 日期类型默认为日（1）时，datePeriod的默认值为7，为周和月(2和3)的时候，默认值为4
      switch (dateTypeRef.current) {
        case busTypes.dateValue:
          datePeriod = datePeriods.dateDefaultValue;
          break;
        case busTypes.weekValue:
          datePeriod = datePeriods.weekDefaultValue;
          break;
        case busTypes.monthValue:
          datePeriod = datePeriods.monthDefaultValue;
          break;
        default:
          break;
      }

      const params = {
        dateType: dateTypeRef.current,
        dateValue,
        deviceId,
        datePeriod,
        busType,
      };

      usageHistory(params).then((res: {entry: EntryProps}) => {
        const {entry} = res;
        rootStore.deviceChartStore.actions.setUsageHistoryData(entry);
        const {totalCo2Reduced} = rootStore.deviceChartStore.actions;
        const {
          totalDrivingDistance,
          totalMovingArea,
          totalPowerConsumption,
          totalmowingTime,
        } = rootStore.deviceChartStore.actions.statistics;
        let valueTextStr = '';
        switch (selectedIndexRef.current) {
          case 0:
            valueTextStr = totalmowingTime;
            break;
          case 1:
            valueTextStr = totalMovingArea;
            break;
          case 2:
            valueTextStr = totalPowerConsumption;
            break;
          case 3:
            valueTextStr = totalDrivingDistance;
            break;
          case 4:
            valueTextStr = totalCo2Reduced;
            break;
          default:
            break;
        }
        setValueText(valueTextStr);
      });
    };

    const onLeftPress = () => {
      tracer.click({eleid: eleIdMap.Return_Button_Click});
      goBack(navigation);
    };

    const filterButtonOnPress = () => {
      setModalVisible(true);
    };

    const onItemSelected = (index: number) => {
      const {isImperial} = rootStore.deviceChartStore.actions;

      let unitStr = Strings.getLang('rn_61004_panelhome_hour_textview_text');
      switch (index) {
        case 0:
          tracer.click({eleid: eleIdMap.Total_Time_Button_Click});
          unitStr = Strings.getLang('rn_61004_panelhome_hour_textview_text');
          break;
        case 1:
          tracer.click({eleid: eleIdMap.Mowing_Area_Button_Click});
          unitStr = isImperial
            ? Strings.getLang('rn_61004_usagehistory_acresunit_textview_text')
            : Strings.getLang('rn_61004_usagehistory_m2unit_textview_text');
          break;
        case 2:
          tracer.click({eleid: eleIdMap.Power_Consumption_Button_Click});
          unitStr = Strings.getLang('rn_common_unit_kwh_textview_text');
          break;
        case 3:
          tracer.click({eleid: eleIdMap.Driving_Distance_Button_Click});
          unitStr = isImperial
            ? Strings.getLang('rn_61004_usagehistory_milesunit_textview_text')
            : Strings.getLang(
                'rn_61004_usagehistory_unitkilometers_textview_text',
              );
          break;
        case 4:
          tracer.click({eleid: eleIdMap.CO2_Reduction_Button_Click});
          unitStr = isImperial
            ? Strings.getLang('rn_61004_usagehistory_lbsunit_textview_text')
            : Strings.getLang('rn_61004_usagehistory_kgunit_textview_text');
          break;
        default:
          break;
      }
      // 设置折线图上面显示的单位
      rootStore.deviceChartStore.actions.setSelectedUnit(unitStr);
      const selectedObj = filterData.find(
        (subItem: {index: number}) => subItem.index === index,
      );
      const selectedSubObj = subTitleData[index] || {};
      setSelectedTitle(selectedObj?.title);
      setSelectedSubTitle(selectedSubObj?.title);
      selectedIndexRef.current = index;
      getUsageHistory();
    };
    const onDateTypeSelect = (index: number) => {
      switch (index) {
        case 0:
          tracer.click({eleid: eleIdMap.Day_Button_Click});
          break;
        case 1:
          tracer.click({eleid: eleIdMap.Week_Button_Click});
          break;
        case 2:
          tracer.click({eleid: eleIdMap.Month_Button_Click});
          break;

        default:
          break;
      }
      dateTypeRef.current = index + 1;
      if (dateTypeRef.current !== lastDateTypeRef.current) {
        lastDateTypeRef.current = dateTypeRef.current;
        getUsageHistory();
      }
    };

    const onHelpPress = () => {
      showModal({
        contentRender: (
          <>
            <Text style={styles.tipStyle}>
              {Strings.getLang(
                'rn_61004_panelhome_usagehistory_help_description_textview_text1',
              )}
            </Text>
            <Text style={[styles.tipStyle, styles.textMarginTop]}>
              {Strings.getLang(
                'rn_61004_panelhome_usagehistory_help_description_textview_text2',
              )}
            </Text>
          </>
        ),
        onConfirm: () => {},
      });
    };

    const rightPress = () => {
      tracer.click({
        eleid: eleIdMap.Device_Details_Button_Click,
      });
      const minDateStr = undefined;
      // 时间格式 '2024/01/19'
      const defaultTime = dayjs().format('YYYY-MM-DDTHH:mm:ss.sssZ');
      const title = Strings.getLang(
        'rn_61004_usagehistory_datepickertitle_textview_text',
      );
      const onConfirmed = (selectedTime = '') => {
        if (isAfterToday(selectedTime)) {
          const toastTitle = Strings.getLang(
            'rn_61004_trackhistory_selectedtimetoast_textview_text',
          );
          mobile.toast(toastTitle);
          return;
        }
        dateStrRef.current = selectedTime;
        getUsageHistory();
        const tmpStr = dayjs(selectedTime).format(FORMATDATE);
        setShowDateStr(tmpStr);
      };
      const onCanceled = () => {};
      if (mobile.openDateTimePicker) {
        mobile.openDateTimePicker(
          {
            minimumDate: minDateStr,
            maximumDate: defaultTime,
            value: defaultTime,
            showSpecificTime: false,
            title,
            mode: 'date',
          },
          onConfirmed,
          onCanceled,
        );
      }
    };
    // 渲染布局时调用
    const handleLayout = (event: {nativeEvent: {layout: {height: number}}}) => {
      const {height} = event.nativeEvent.layout;
      /**
       * 瀑布刘海屏，isHasNotch ： false，显示不正常  currentHeight 28高度
       * 打孔屏 isHasNotch： false， 显示正常  currentHeight 45高度
       */
      let tmpHeight = height;
      if (Platform.OS === 'android') {
        const brand = DeviceInfo.getBrand();

        // 安卓布局是从statusbar下面开始的
        if (StatusBar.currentHeight) {
          tmpHeight = height - StatusBar.currentHeight;
        }
        // 单独处理三星交互效果
        if (brand === 'samsung' && StatusBar.currentHeight) {
          tmpHeight = height - StatusBar.currentHeight - 25;
        }
      }
      setHeaderHeight(tmpHeight);
    };
    const {selectedUnit} = rootStore.deviceChartStore.state;
    return (
      <PageView>
        <HeaderView
          onLayout={handleLayout}
          title={Strings.getLang('rn_61004_usagehistory_title_textview_text')}
          useCommonEleId={false}
          onLeftPress={onLeftPress}
          rightElement={
            <View style={styles.rightItemStyle}>
              <TouchableOpacity
                onPress={rightPress}
                hitSlop={{top: 20, right: 20, left: 20, bottom: 20}}>
                <View style={styles.rightBack}>
                  <CVNIcon
                    size={26}
                    source={require('@assets/common/61004_common_icon_date.png')}
                  />
                </View>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={onHelpPress}
                hitSlop={{top: 20, right: 20, left: 20, bottom: 20}}>
                <View>
                  <CVNIcon
                    size={26}
                    source={require('@assets/history/61004_icon_usehistory_help.png')}
                  />
                </View>
              </TouchableOpacity>
            </View>
          }
        />
        <ScrollView
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.container}>
          <View style={styles.selectBar}>
            <TouchableWithoutFeedback onPress={filterButtonOnPress}>
              <View style={styles.topView}>
                <Text style={styles.topTitle}>{selectedTitle}</Text>
                <CVNIcon
                  size={20}
                  style={styles.topIcon}
                  source={require('@assets/history/61004_icon_arrow_down_black.png')}
                />
              </View>
            </TouchableWithoutFeedback>
            <Text style={styles.dateStrText}>{showDateStr}</Text>
          </View>
          <View style={styles.chartContainerBox}>
            <View style={styles.workTimeBox}>
              <Text style={styles.titleText}>{selectedSubTitle}</Text>
              <Text style={styles.valueText}>{valueText}</Text>
            </View>
            <DateTypeSegment onItemSelect={onDateTypeSelect} />
            <BaseLineChart
              selectedUnit={selectedUnit}
              style={styles.cChart}
              type="static"
              chartData={rootStore.deviceChartStore.state.chartData}
            />
          </View>
          <WhiteSpace size={isIphoneX ? 34 : 10} />
        </ScrollView>
        <Dropdown
          defaultIndex={0}
          visible={modalVisible}
          setModalVisible={setModalVisible}
          onItemSelected={onItemSelected}
          data={filterData}
          // eslint-disable-next-line react-native/no-inline-styles
          modalViewstyle={{marginTop: headerHeight ? headerHeight + 45 : 45}}
          onClose={() => setModalVisible(false)}
        />
      </PageView>
    );
  }),
);

const ViewUsageHistory = withCounter(MyClassComponent);

export default ViewUsageHistory;
const styles = StyleSheet.create({
  container: {
    marginHorizontal: 14,
  },
  selectBar: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  topView: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 6,
    marginTop: 20,
  },
  topTitle: {
    color: '#000000',
    fontWeight: '500',
    fontSize: 16,
  },
  rightItemStyle: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  topIcon: {
    marginLeft: 10,
  },
  chartContainerBox: {
    marginTop: 16,
    paddingTop: 20,
    minHeight: 356 + 20,
    paddingBottom: 30,
    backgroundColor: '#fff',
    borderRadius: 10,
    flex: 1,
  },
  rightBack: {
    marginRight: 13,
  },
  workTimeBox: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 16,
    justifyContent: 'space-between',
  },
  titleText: {
    fontSize: 15,
    color: '#000000',
  },
  dateStrText: {
    color: '#999',
    marginTop: 20,
  },
  valueText: {
    color: '#77BC1F',
    fontSize: 16,
    fontWeight: '500',
  },
  cChart: {
    marginTop: 8,
    marginHorizontal: 24,
  },
  tipStyle: {
    fontSize: 14,
    fontWeight: '400',
    lineHeight: 19,
    color: '#000000',
  },
  textMarginTop: {
    marginTop: 15,
  },
});
