import React from 'react';
import {Text, View, ScrollView, StyleSheet} from 'react-native';
import {inject, observer} from 'mobx-react';
import {PageView} from 'cvn-panel-kit';
import PropTypes from 'prop-types';
import {Item, HeaderView} from '@components';
import Strings from '@i18n';
import {goBack} from 'IOTRN/src/utils/pageAction';
import {InjectStore} from 'IOTRN/src/utils/interface';

const Statistics = inject('rootStore')(
  observer(({rootStore, navigation}: InjectStore) => {
    const getData = () => {
      const {staticsTotalCo2Reduced} = rootStore.deviceChartStore.state;
      const {
        totalDrivingDistance,
        totalMovingArea,
        totalWorkingTime,
        totalPowerConsumption,
        totalmowingTime,
      } = rootStore.deviceChartStore.actions.statistics;
      return [
        {
          title: Strings.getLang(
            'rn_61004_panelhome_totalworkingtime_textview_text',
          ),
          content: totalWorkingTime,
        },
        {
          title: Strings.getLang(
            'rn_61004_panelhome_statics_driving_distance_textview_text',
          ),
          content: totalDrivingDistance,
        },
        {
          title: Strings.getLang(
            'rn_61004_usagehistory_cardtitletotalmowingarea_textview_text',
          ),
          content: totalMovingArea,
        },
        {
          title: Strings.getLang(
            'rn_61004_usagehistory_alerttotaltime_textview_text',
          ),
          content: totalmowingTime,
        },
        {
          title: Strings.getLang(
            'rn_61004_panelhome_statics_power_consumption_textview_text',
          ),
          content: totalPowerConsumption,
        },
        {
          title: Strings.getLang(
            'rn_61004_panelhome_statics_total_co2reduce_textview_text',
          ),
          content: staticsTotalCo2Reduced,
        },
      ];
    };

    return (
      <PageView>
        <HeaderView
          title={Strings.getLang(
            'rn_common_detaillist_statistics_textview_text',
          )}
          onLeftPress={() => {
            goBack(navigation);
          }}
        />
        <ScrollView style={styles.scrollViewStyle}>
          <Card data={getData()} />
        </ScrollView>
      </PageView>
    );
  }),
);

export default Statistics;

export const Card = ({
  data = [],
}: {
  data: {title: string; content: string}[];
}) => {
  return (
    <View style={styles.cardBox}>
      {data.map(item => {
        return (
          <Item
            iconShow={false}
            key={item.title}
            title={item.title}
            titleStyle={styles.titleStyle}
            rightElement={<Text style={styles.rightText}>{item.content}</Text>}
            lineStyle={styles.lineStyle}
          />
        );
      })}
    </View>
  );
};
Card.propTypes = {
  data: PropTypes.array,
};
const styles = StyleSheet.create({
  cardBox: {},
  rightText: {
    color: '#666666',
    fontSize: 15,
    marginRight: 5,
  },
  scrollViewStyle: {
    backgroundColor: '#ffffff',
    marginTop: 10,
  },
  titleStyle: {
    color: '#000000',
    fontSize: 15,
  },
  lineStyle: {
    marginLeft: 20,
  },
});
