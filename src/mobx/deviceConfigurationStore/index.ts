/**
 * 聚合模块
 * 设备配置与控制
 * **功能范围**：设备参数配置（如延时关灯、报警模式）、OTA升级等。
 */

import {makeAutoObservable} from 'mobx';
import DeviceConfigurationState from './state';
import DeviceConfigurationActions from './actions';
import {RootStore} from '../rootStore';
import {BaseStore} from '../base/BaseStore';

export default class DeviceConfigurationStore extends BaseStore {
  state: DeviceConfigurationState;
  actions: DeviceConfigurationActions;
  constructor(rootStore: RootStore) {
    super(rootStore);
    this.state = new DeviceConfigurationState();
    this.actions = new DeviceConfigurationActions(this.state, rootStore);
    makeAutoObservable(this);
  }
  /**
   * 重置设备配置状态
   *
   * 将当前状态重置为新的 DeviceConfigurationState 实例
   */
  reset = () => {
    this.state = new DeviceConfigurationState();
  };
}
