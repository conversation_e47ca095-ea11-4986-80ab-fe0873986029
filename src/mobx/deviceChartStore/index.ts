import {makeAutoObservable} from 'mobx';
import DeviceCharState from './state';
import DeviceCharActions from './actions';
import {RootStore} from '../rootStore';
import {BaseStore} from '../base/BaseStore';

/**
 * 聚合模块
 * 设备数据统计图表
 * **功能范围**：历史数据统计、图表数据源、单位转换等。
 */
export default class DeviceCharStore extends BaseStore {
  state: DeviceCharState;
  actions: DeviceCharActions;

  constructor(rootStore: RootStore) {
    super(rootStore);
    this.state = new DeviceCharState();
    this.actions = new DeviceCharActions(this.state, rootStore);
    makeAutoObservable(this);
  }

  /**
   * 重置当前组件的状态
   */
  reset = () => {
    this.state = new DeviceCharState();
  };
}
