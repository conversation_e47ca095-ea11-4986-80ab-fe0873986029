/**
 * 聚合模块
 * 设备基础信息
 * **功能范围**：设备基础参数、连接状态、国际化配置等。
 */

import {makeAutoObservable} from 'mobx';
import DeviceStoreState from './state';
import DeviceBasicInfoActions from './actions';
import {BaseStore} from '../base/BaseStore';
import {RootStore} from '../rootStore';

export default class DeviceStore extends BaseStore {
  state: DeviceStoreState;
  actions: DeviceBasicInfoActions;
  // 构造函数，接收一个RootStore类型的参数
  constructor(rootStore: RootStore) {
    super(rootStore);
    this.state = new DeviceStoreState();
    this.actions = new DeviceBasicInfoActions(this.state);
    makeAutoObservable(this);
  }

  /**
   * 重置当前组件的状态
   */
  reset = () => {
    this.state = new DeviceStoreState();
  };
}
