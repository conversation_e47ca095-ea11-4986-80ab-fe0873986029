import {computed} from 'mobx';
import DeviceBasicInfoState from './state';
import {getOnceDataWithWIFI, subscribeWithWIFI} from '../utils/help';
import {DeviceDetailProps, InitialParamsProps} from 'IOTRN/src/types/device';
import {parseJsonSafely} from '../../utils/jsonParser';

const TOTAL_HOURS = 24;
export default class DeviceBasicInfoActions {
  state: DeviceBasicInfoState;
  constructor(state: DeviceBasicInfoState) {
    this.state = state;
  }

  // 设备id
  @computed get deviceId() {
    const {deviceId} = this.state.initialParams;
    return deviceId;
  }
  // 设备mac地址
  @computed get mac() {
    const {mac} = this.state.initialParams;
    return mac;
  }

  // 设备名字
  @computed get deviceName() {
    let {deviceName} = this.state.initialParams;
    if (!deviceName || (deviceName?.length || 0) === 0) {
      deviceName = this.state.deviceDetail?.deviceName;
    }
    return deviceName;
  }

  @computed get nickName() {
    const {deviceDetail} = this.state.initialParams;
    let {nickName} = this.state.deviceDetail;
    if (deviceDetail !== '' && deviceDetail.length > 0) {
      // 使用安全的JSON解析，提供默认值和错误处理
      const deviceDetailObj = parseJsonSafely(
        deviceDetail,
        {} as {nickName?: string}, // 默认空对象，添加类型注解
        'DeviceStore.nickName',
      );
      if (!nickName || (nickName?.length || 0) === 0) {
        nickName = deviceDetailObj?.nickName || '';
      }
    }
    return nickName;
  }

  // 是否是已经注册
  @computed get deviceDidRegisted() {
    return this.state.deviceDetail?.infoStatus - 0 === 1;
  }

  // 设备分享 0: 不属于主子账户；1: 主设备；2：子设备 仅‘主设备’展示设备分享按钮
  @computed get canBeShared() {
    const {deviceDetail} = this.state.initialParams;
    let {shareType} = this.state.deviceDetail;
    if (shareType === undefined) {
      const defaultDeviceDetail = {shareType: 0};
      if (deviceDetail !== '' && deviceDetail.length > 0) {
        // 使用安全的JSON解析，提供默认值和错误处理
        const deviceDetailObj = parseJsonSafely(
          deviceDetail,
          defaultDeviceDetail, // 默认值
          'DeviceStore.canBeShared',
        );
        shareType = deviceDetailObj?.shareType ?? 0;
      } else {
        shareType = defaultDeviceDetail.shareType;
      }
    }
    return shareType === 1;
  }

  // 产品id
  @computed get productId() {
    let {productId} = this.state.initialParams;
    if (!productId || productId?.length === 0) {
      productId = this.state.deviceDetail?.productId;
    }
    return productId;
  }

  // 北美12小时，欧洲24小时
  @computed get appSettingOfHour() {
    const {appSettingOfHour} = this.state.initialParams;
    return appSettingOfHour;
  }
  // 地区 NA/EU  EU:欧洲
  @computed get region() {
    const {region} = this.state.initialParams;
    return region;
  }
  /**
   * 时区：0-23
   * 0 ：0时区，1~12：东1区~东12区，13~23：西1区~西11区
   */
  @computed get timezone() {
    const {timezone} = this.state.initialParams;
    return Number(timezone || 0);
  }
  /**
   * @description:  刻度，获得选中的小时和分钟
   */
  @computed get selectedTimeInfo() {
    const {selectedTick = 0} = this.state;
    const selectedTime = selectedTick % (TOTAL_HOURS * 60);
    const hours = Math.floor(selectedTime / 60);
    const minutes = selectedTime % 60;
    return {
      hours,
      minutes,
    };
  }
  /**
   * @description: 原生传递参数后初始化
   * @param {Object} params
   */
  initParams = (params: InitialParamsProps) => {
    this.state.initialParams = params;
  };

  /**
   * @description: 设置当前设备配置的WiFi名称
   * @param {String} wifiName
   */
  setWifiName = (name: string) => {
    this.state.wifiName = name;
  };

  /**
   * @description: 设置详情数据
   * @param {Object} params
   */
  setDetail = (params: DeviceDetailProps) => {
    this.state.deviceDetail = params;
  };

  /**
   * WIFI 单词获取数据
   */
  getOnceDataWithWIFI = () => {
    getOnceDataWithWIFI({deviceId: this.state.initialParams.deviceId});
  };
  /**
   * topic订阅
   */
  subscribeWithWIFI = () => {
    subscribeWithWIFI({deviceId: this.state.initialParams.deviceId});
  };

  /**
   * @description: 在仪表盘上设置选定的刻度
   * @param {number} value - 选定的刻度值
   */
  setSelectedTick = (value = 0) => {
    this.state.selectedTick = value;
  };
}
