import {makeAutoObservable} from 'mobx';
import DeviceAbnormalState from './state';
import DeviceAbnormalActions from './actions';
import {RootStore} from '../rootStore';
import {BaseStore} from '../base/BaseStore';

/**
 * 聚合模块
 * 设备故障与报警
 * **功能范围**：设备故障列表、报警状态、紧急联系人等。
 */
export default class DeviceAbnormalStore extends BaseStore {
  state: DeviceAbnormalState;
  actions: DeviceAbnormalActions;
  constructor(rootStore: RootStore) {
    super(rootStore);
    this.state = new DeviceAbnormalState();
    this.actions = new DeviceAbnormalActions(this.state, rootStore);
    makeAutoObservable(this);
  }
  /**
   * 重置函数
   */
  reset = () => {
    this.state = new DeviceAbnormalState();
  };
}
