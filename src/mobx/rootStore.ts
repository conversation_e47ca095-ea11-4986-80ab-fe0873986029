import {makeAutoObservable} from 'mobx';
import BatteryManagementStore from './batteryStore';
import DeviceStore from './deviceStore';
import DeviceConfigurationStore from './deviceConfigurationStore';
import DeviceCharStore from './deviceChartStore';
import DeviceAbnormalStore from './deviceAbnormalStore';
import DeviceConnectionStore from './deviceConnectionStore';
import {getDeviceDetail} from '../api';
import {DeviceDetailProps} from '../types/device';

// 导出默认的RootStore类
export class RootStore {
  // 定义电池管理存储
  batteryManagementStore: BatteryManagementStore;
  // 定义设备基本信息存储
  deviceStore: DeviceStore;
  // 定义设备配置和控制存储
  deviceConfigurationStore: DeviceConfigurationStore;
  // 定义设备数据统计图表存储
  deviceChartStore: DeviceCharStore;
  // 定义设备故障和报警存储
  deviceAbnormalStore: DeviceAbnormalStore;
  // 定义设备状态和连接存储
  deviceConnectionStore: DeviceConnectionStore;
  // 构造函数
  constructor() {
    // 使用makeAutoObservable自动创建observable和action
    makeAutoObservable(this);
    // 初始化电池管理存储
    this.batteryManagementStore = new BatteryManagementStore(this);
    // 初始化设备基本信息存储
    this.deviceStore = new DeviceStore(this);
    // 初始化设备状态和连接存储
    this.deviceConnectionStore = new DeviceConnectionStore(this);
    // 初始化设备配置和控制存储
    this.deviceConfigurationStore = new DeviceConfigurationStore(this);
    // 初始化设备数据统计图表存储
    this.deviceChartStore = new DeviceCharStore(this);
    // 初始化设备故障和报警存储
    this.deviceAbnormalStore = new DeviceAbnormalStore(this);
  }
  /**
   * 请求设备详细信息
   *
   * @param params 请求参数，包含设备请求字符串
   * @param callBack 请求成功后的回调函数，可选
   */
  requestDeviceDetail = (params: {req: string}, callBack?: () => void) => {
    getDeviceDetail(params).then((res: {entry: DeviceDetailProps}) => {
      this.deviceStore.actions.setDetail(res?.entry);
      this.deviceConnectionStore.actions.setWIFIConnect(
        Number(res?.entry?.isOnline) === 1,
      );
      /**
       * 如何设备没发送过来升级版本号,设置后端发送过来的版本号
       */
      if (!this.deviceConfigurationStore.state.showRed) {
        this.deviceConfigurationStore.actions.setCustomVersion(
          res.entry.version,
        );
      }
      callBack?.();
    });
  };
  /**
   * 重置所有存储。
   *
   * 该方法将调用各个存储对象的reset方法，以重置存储中的数据。
   */
  reset = () => {
    this.batteryManagementStore.reset();
    this.deviceStore.reset();
    this.deviceConnectionStore.reset();
    this.deviceConfigurationStore.reset();
    this.deviceChartStore.reset();
    this.deviceAbnormalStore.reset();
  };
}
const rootStore = new RootStore();
export default rootStore;
