/*
 * @Author: 邢立伟 <EMAIL>
 * @Date: 2024-08-09 16:17:28
 * @LastEditors: 邢立伟 <EMAIL>
 * @LastEditTime: 2024-09-10 18:37:22
 * @FilePath: /61004/src/mobx/actions/help
 * @Description: action相关的工具类
 */
import {device, mobile} from 'cvn-panel-kit';
import {JumpUtils, PermissionUtils, StringUtils} from 'cvn-panel-kit/src/utils';
import {omitBy, isArray, isObject} from 'lodash-es';
import dayjs from 'dayjs';
import DpDataUtils from 'IOTRN/src/utils/dpData';
import ModelMap from '@utils/model';
import {getNextQuarterIndexFromTimeStamp} from 'IOTRN/src/utils/range';
import {convertMonthName, formatMinutes, isToday} from 'IOTRN/src/utils/time';
import Strings from 'IOTRN/src/i18n';
import {
  cmPerSecondToMph,
  convertMeterToMiles,
  squareMeterToAcre,
  squareMeterToAcreStr,
} from 'IOTRN/src/utils/transform';
import {gramsToKilograms, gramsToPounds} from 'IOTRN/src/utils/grams';
import {isHexString} from 'IOTRN/src/utils/string';
import {mToKm, mphToKmh} from 'IOTRN/src/utils/unit';
import {BUSTYPEDEFAULTVALUE} from '@utils/constant';
import topicMap from 'IOTRN/src/utils/topic';
import {EntryProps} from './interfaceProps';

const noErrorCode = '190000';

/**
   * @description: 组装成desired的值，给topic接口最终数据结构： { desired: { 34: {1: 1} } }
   * @param {Object} propertyData
   * @example
   * const obj = { 34: 1, 35: 2 };
   * const result = getDataFromPropertyData(obj);
   * console.log(result);
   * {
        "34":{
            "1":1
        },
        "35":{
            "1":2
        }
    }
   * @return {Object} result
   */
export const getDataFromPropertyData = (propertyData: {}) => {
  const result: {[key: string]: object} = {};
  Object.keys(propertyData).forEach((key: string) => {
    const resultValue = formatDataValue(propertyData, key);
    result[key] = {
      1: resultValue,
    };
  });
  return result;
};
/**
 * @description: format WIFI通道的值
 * @param {Object} propertyData
 * @param {String} key
 * @return {Object} result
 */
export const formatDataValue = (
  propertyData: {[x: string]: string},
  key: string,
) => {
  const result = propertyData[key];
  return result;
};

/**
 * @description: WIFI set属性值
 * @param {Object} propertyData
 * @param {Object} panel - 当前面板对象
 */
export const editPropertyWithWIFI = (
  propertyData: object,
  panel: {deviceId: string},
) => {
  if (device.editProperty) {
    const data = getDataFromPropertyData(propertyData);
    const params = {
      state: {
        desired: data,
      },
    };
    const paramsStr = JSON.stringify(params);
    const {deviceId} = panel;
    const topicStr = `${topicMap.preFix}${deviceId}${topicMap.shadowUpdateSuffix}`;

    // 返回没用，以topicDidChange数据返回为准,去掉.then，安卓原生应该是有改动，.then去掉
    device.editProperty(topicStr, paramsStr);
  }
};
/**
 * 统一处理数据结构，根据蓝牙连接状态选择数据处理方式。
 * 如果当前有蓝牙连接，则通过蓝牙进行数据编辑；如果没有，则通过云端进行数据编辑。
 * @param {Object} params - 要编辑的属性参数对象，包含必要的属性信息。
 * @param {Object} panel - 当前面板对象，用于指定编辑上下文。
 * @param {Function} callback - 编辑完成后的回调函数，通常用于隐藏加载状态或进行后续操作。
 */
export const editProperty = (
  params: {propertyData: {[key: string]: number | undefined}},
  panel: {bleConnected: boolean; connected: boolean; deviceId: string},
  callBack: (res: string[]) => void,
) => {
  const {propertyData} = params;
  // 过滤数据
  const data = omitBy(propertyData, item => item === undefined);
  handleLoading(data, panel, callBack);
  const {bleConnected, connected} = panel;
  // 判断是否是蓝牙连接,优先蓝牙通道
  if (bleConnected) {
    // {[x: string]: {paramId: string; paramData: number | boolean}[]}
    DpDataUtils.editPropertyWithBle(data, resultData => {
      if (resultData?.length === 0) {
        console.warn('蓝牙通道数据不能为空数组');
        return;
      }
      setLocalProperty(resultData);
    });
  } else if (connected) {
    editPropertyWithWIFI(data, panel);
  }
};
/**
 * 处理加载状态，控制加载提示的显示与隐藏。
 * @param {Object} data - 过滤后的数据对象，可能包含需要处理的命令或状态信息。
 * @param {Object} panel - 当前面板对象。
 * @param {Function} callBack - 加载开始后的回调函数，通常用于执行一些额外操作（如更新状态）。
 */
export const handleLoading = (
  data: {},
  panel: {bleConnected: boolean; connected: boolean},
  callBack: (res: string[]) => void,
) => {
  const {bleConnected, connected} = panel;
  if (bleConnected || connected) {
    const delaySeconds = 3000; // 默认3s,可能会发命令期间收到数据上报，loading消失
    // loading处理, 后面需要全局加的话，把下面过滤条件去掉即可
    if (
      Object.keys(data).indexOf(ModelMap.remote_control_angle) !== -1 ||
      Object.keys(data).indexOf(ModelMap.modify_password) !== -1 ||
      Object.keys(data).indexOf(ModelMap.slope_alarm) !== -1 ||
      Object.keys(data).indexOf(ModelMap.reset_pwd) !== -1
    ) {
      const tmpKeys = Object.keys(data);
      callBack(tmpKeys);
      mobile.showLoading();
      global.hasLoading = true; // cvn-panel-kit中也有，全局一个loading
      if (global.loadingTimer) {
        clearTimeout(global.loadingTimer);
      }
      // 设置3s超时，不管是否有loading，都调用一下
      global.loadingTimer = setTimeout(() => {
        mobile.hideLoading();
        global.hasLoading = false;
      }, delaySeconds);
    }
  }
};
/**
 * @description: 遥控模式走蓝牙协议,再此统一封装成需要的数据格式
 * @param {Array} data
 */
export const setLocalProperty = (data: object) => {
  return new Promise(resolve => {
    const jsonStr = JSON.stringify(data);
    device.setLocalDeviceData(jsonStr).then((res: unknown) => {
      resolve(res);
    });
  });
};
/**
 * WIFI 单词获取数据
 * @param {Object} panel - 当前面板对象。
 */
let publishTimer: NodeJS.Timeout | undefined | number;
export const getOnceDataWithWIFI = (panel: {deviceId: string}) => {
  // 单次获取物模型所有属性，通过监听方法返回
  const {deviceId} = panel;
  const shadowStr = `${topicMap.preFix}${deviceId}${topicMap.shadowGet}`;
  if (device.topicPublish) {
    if (publishTimer) {
      clearTimeout(publishTimer);
    }
    publishTimer = setTimeout(() => {
      device.topicPublishV2(shadowStr, '', 0); // 0 默认等级
    }, 1000);
  }
};
/**
 * topic订阅
 *  @param {Object} panel - 当前面板对象。
 */
export const subscribeWithWIFI = (panel: {deviceId: string}) => {
  const {deviceId} = panel;
  if (device.subscribe) {
    const connectWIFITopic = `${topicMap.connectWIFI}${deviceId}`;
    const disConnectWIFITopic = `${topicMap.disConnectWIFI}${deviceId}`;
    const shadowUpdateAcceptedStr = `${topicMap.preFix}${deviceId}${topicMap.shadowUpdateAcceptedSuffix}`;

    // 新增 单次拉取返回
    const shadowGetAcceptedStr = `${topicMap.preFix}${deviceId}${topicMap.shadowGetAcceptedSuffix}`;

    // WIFI连接
    device.subscribe(connectWIFITopic);
    // WIFI断开
    device.subscribe(disConnectWIFITopic);
    // 订阅物模型 接收
    device.subscribeV2(shadowUpdateAcceptedStr, 0); // 0 默认等级，如果需要防止数据丢失，可考虑改成1，
    // 物模型订阅，单次拉取
    device.subscribe(shadowGetAcceptedStr);
  }
};
/**
 * @description: 解析topic回来之后的物模型对应的值
 * @param {Object} reportedMap
 * @param {String} key
 * @return {Object} result
 */
export const getTopicDpData = (
  reportedMap: {[x: string]: string},
  key: string,
) => {
  if (isArray(reportedMap)) {
    console.warn('WIFI返回数据结构错误');
    return undefined;
  }
  // 优先取值 多参的，如果取不到，按照属性获取，一层
  const tmpData: {[1]: string} | string = reportedMap[key];
  let result = tmpData ?? '';

  if (isObject(tmpData)) {
    result = tmpData['1'] ?? '';
  }
  return result;
};
/**
 * @description 获取对象组成部分
 * @param {Object} value
 * @param {Object} panel - 当前面板对象。
 * @returns Object
 */
export const getInfoOfMessage = (
  value: {
    title: string;
    payloadData: {
      uuid: string;
      productId: string;
      messageType: string;
      createTime: string;
      deviceId: string;
      eventType: string;
      data: string;
    };
  },
  panel: {homeMsgList: {[key: string]: string}[]},
) => {
  const {title, payloadData} = {...value};
  const {data} = payloadData;
  let dataObj: {[key: string]: {[key: string]: string}} = {};
  try {
    if (data) {
      dataObj = JSON.parse(data) ?? {};
    }
  } catch (error) {
    console.warn('数据源不是标准json' + error);
  }
  const {homeMsgList = []} = panel;
  const errorObj = dataObj ? dataObj['44005'] : {};
  const errorCode = errorObj ? errorObj['1'] : '';
  const errorValue = errorObj ? errorObj['2'] : '';
  const filteredObj = homeMsgList.find(
    (item: {[key: string]: string}) => `${item.faultCode}` === `${errorCode}`,
  );
  const {uuid, productId, messageType, createTime, deviceId, eventType} =
    payloadData;
  const newObj = {
    faultTitle: title,
    faultCode: errorCode,
    productId,
    messageType,
    createTime,
    deviceId,
    uuid, // 这里和接口返回不一样，接口里面为faultMessageId
    eventType,
    ...filteredObj,
  };
  return {filteredObj, newObj, errorCode, errorValue};
};
// 根据errorCode做筛选
export const getNewResultFromErrorCode = ({
  filteredObj,
  newObj,
  homeMsgList,
  errorCode,
  errorValue,
}: {
  filteredObj: {[key: string]: string};
  newObj: {[key: string]: string};
  homeMsgList: {[key: string]: string}[];
  errorCode: string;
  errorValue: string;
}) => {
  let result = [...homeMsgList];
  // 19XX00：“模块无故障”，支持针对某一模块进行模块故障清除，清除一组数据,不管value是否为true
  const errorCodeStr = `${errorCode}`;
  if (
    errorCodeStr.length === 6 &&
    errorCodeStr.slice(2, 4) !== '00' &&
    errorCodeStr.slice(4, 6) === '00'
  ) {
    const startFourStr = errorCodeStr.slice(0, 4);
    result = homeMsgList.filter(item => {
      const faultCodeStr = `${item.faultCode}`;
      const tmpResult = !faultCodeStr.startsWith(startFourStr);
      return tmpResult;
    });
  } else if (errorValue) {
    // 如果原来中有，不处理，如果原来没有，新增
    if (!filteredObj) {
      result = [newObj, ...homeMsgList];
    }
  } else {
    result = homeMsgList.filter(item => `${item.faultCode}` !== `${errorCode}`);
  }
  // 如果code码值为全部消除，190000,优先级最高
  if (`${errorCode}` === noErrorCode) {
    result = [];
  }

  return result;
};

/**
 * @description 根据数据类型和单位制转换数值
 * @param {Number} value
 * @param {Number} busType
 * @param {boolean} isImperial
 * @returns {Object} convertedValue, unit
 */
export const convertValue = (
  value: string,
  busType: string,
  isImperial: boolean,
) => {
  let convertedValue: number | string = 0;
  const newValue = Number(value);
  switch (busType) {
    case BUSTYPEDEFAULTVALUE.mowingTime?.[0]:
      convertedValue = Number((newValue / 3600).toFixed(2));
      return {
        convertedValue,
        unit: Strings.getLang('rn_61004_usagehistory_hour_textview_text'),
      };
    case BUSTYPEDEFAULTVALUE.c02Reduction?.[0]: {
      const grams = newValue || 0;
      const kilograms = gramsToKilograms(grams);
      const pounds = gramsToPounds(grams);
      if (isImperial) {
        convertedValue = Number(pounds).toFixed(2);
        return {
          convertedValue,
          unit: Strings.getLang('rn_61004_usagehistory_lbsunit_textview_text'),
        };
      } else {
        convertedValue = Number(kilograms).toFixed(2);
        return {
          convertedValue,
          unit: Strings.getLang('rn_61004_usagehistory_kgunit_textview_text'),
        };
      }
    }
    case BUSTYPEDEFAULTVALUE.cuttingArea?.[0]: {
      const squareMeters = newValue || 0;
      const acres = squareMeterToAcreStr(squareMeters);
      if (isImperial) {
        convertedValue = Number(acres).toFixed(2);
        return {
          convertedValue,
          unit: Strings.getLang(
            'rn_61004_usagehistory_acresunit_textview_text',
          ),
        };
      } else {
        convertedValue = squareMeters;
        return {
          convertedValue,
          unit: Strings.getLang('rn_61004_usagehistory_m2unit_textview_text'),
        };
      }
    }
    case BUSTYPEDEFAULTVALUE.drivingDistance?.[0]: {
      const distance = newValue || 0;
      const kilometers = distance / 1000;
      const miles = convertMeterToMiles(distance);
      if (isImperial) {
        convertedValue = miles;
        return {
          convertedValue,
          unit: Strings.getLang(
            'rn_61004_usagehistory_milesunit_textview_text',
          ),
        };
      } else {
        convertedValue = kilometers;
        return {
          convertedValue,
          unit: Strings.getLang(
            'rn_61004_panelhome_distancekilometers_textview_text',
          ),
        };
      }
    }
    case BUSTYPEDEFAULTVALUE.powerConsumption?.[0]: {
      const consumption = newValue || 0;
      // wh -- >  kwh
      const convertedValueNew = consumption / 1000;
      return {
        convertedValueNew,
        unit: Strings.getLang('rn_common_unit_kwh_textview_text'),
      };
    }
    default:
      return {
        convertedValue,
        unit: '',
      };
  }
};

/**
 * @description 根据日期类型转换标签
 * @param {String} key
 * @param {Number} dateType
 * @returns String
 */
export const convertLabel = (key: string, dateType: number) => {
  if (Number(dateType) === 1) {
    if (isToday(key)) {
      return 'Today';
    } else {
      const date = dayjs(key);
      return date.format('MMM/D');
    }
  } else if (dateType === 2) {
    if (key.toLowerCase() === 'this week') {
      return 'This,Week';
    } else {
      const trimedKey = key.trim();
      const strArray = trimedKey.split('-');
      const startStr = strArray.length > 0 ? strArray[0] : '';
      const endStr = strArray.length > 1 ? strArray[1] : '';
      const startDate = dayjs(startStr);
      const endDate = dayjs(endStr);
      const formattedStartDate = startDate.format('MMM/D');
      const formattedEndDate = endDate.format('MMM/D');
      return `${formattedStartDate},-,${formattedEndDate}`;
    }
  } else {
    return convertMonthName(key);
  }
};
/**
 * @description: format折线图的数据
 * dateType: 1\2\3  day\week\month
 * busType: 1\2\3  时间\co2\面积
 */
export const getChartData = (
  info: EntryProps,
  panel: {isImperial: boolean},
) => {
  const {dataList, totalValue: total, dateType = 0} = info;
  let unit = '';
  const labels: string[] = [];
  const yData: number[] = [];
  const busType = Object.keys(total)[0] || BUSTYPEDEFAULTVALUE.mowingTime[0];
  const totalValue = total[busType];
  const data = Array.isArray(dataList[busType]) ? dataList[busType] : [];

  (data || []).forEach((item: {key: string; value: string}) => {
    const {key = '', value} = item;
    const {isImperial} = panel;
    const {convertedValue, unit: convertedUnit} = convertValue(
      value,
      busType,
      isImperial,
    );
    unit = convertedUnit;
    let convertedValueNew = Number(convertedValue);
    if (isNaN(convertedValueNew)) {
      convertedValueNew = 0;
    }
    yData.push(convertedValueNew);
    const label = convertLabel(key, dateType);
    labels.push(label);
  });

  // Jackson要求 数据都是0时，显示折线图，而不是显示No Content
  // const zeroNum = data?.length || 0;
  // let tmpNum = 0;
  // (data || []).forEach(item => {
  //   if (item?.value - 0 === 0) {
  //     tmpNum++;
  //   }
  // });
  // if (zeroNum === tmpNum && zeroNum !== 0) {
  //   yData = [];
  // }

  const result = {
    labels,
    data: yData,
    unit,
    totalValue,
  };
  return result;
};

/**
 * @description: 获取trackhistory下面的割草数据,
 * 此处区分公制和英制单位转化以及小数掉保留位数
 * @param {Object} info
 */
export const getHistoryMowingData = (
  info: {
    listCoordinateHistory?: string;
    averageSpeed?: number;
    cuttingTime?: number;
    cuttingArea?: number;
    drivingDistance?: number;
    maxSpeed?: number;
  },
  panel: {isImperial: string},
) => {
  const {
    averageSpeed: averageSpeedCmPerSecond = 0, // cm/s  7.19改
    cuttingTime = 0, // 设备返回 单位s
    cuttingArea = 0, // 设备返回平方米
    drivingDistance = 0, // 单位厘米 7.12修改 --> 7.19改成单位米
    maxSpeed: maxSpeedCmPerSecond = 0, // cm/s 7.19改
  } = info;
  const averageSpeed = cmPerSecondToMph(averageSpeedCmPerSecond);
  const maxSpeed = cmPerSecondToMph(maxSpeedCmPerSecond);
  // 下面这些是英制
  const cuttingMinutesOfImperial = formatMinutes(Math.round(cuttingTime / 60));
  const averageSpeedOfImperial = Number(averageSpeed || 0.0).toFixed(1);
  const maxSpeedOfImperial = Number(maxSpeed || 0.0).toFixed(1);
  const cuttingAreaOfImperial = squareMeterToAcre(
    Number(cuttingArea ?? 0),
  ).toFixed(2);
  const drivingDistanceOfImperial =
    convertMeterToMiles(drivingDistance).toFixed(1);
  // 下面这些是公制
  const averageSpeedOfMetric = mphToKmh(Number(averageSpeed || 0.0)).toFixed(1);
  const maxSpeedOfOfMetric = mphToKmh(Number(maxSpeed || 0.0)).toFixed(1);
  const cuttingAreaOfMetric = Number(cuttingArea ?? 0).toFixed(0);
  const drivingDistanceOfMetric = mToKm(Number(drivingDistance ?? 0.0)).toFixed(
    1,
  );
  const {isImperial} = panel;
  const result = [1, 2, 3, 4, 5].map((_, index) => {
    let title;
    let value;
    switch (index) {
      case 0:
        title = Strings.getLang(
          'rn_61004_trackhistory_cuttingarea_textview_text',
        );
        if (isImperial) {
          value = `${cuttingAreaOfImperial} ${Strings.getLang(
            'rn_61004_trackhistory_acreunit_textview_text',
          )}`;
        } else {
          value = `${cuttingAreaOfMetric} ${Strings.getLang(
            'rn_61004_trackhistory_m2unit_textview_text',
          )}`;
        }
        break;
      case 1:
        title = Strings.getLang(
          'rn_61004_trackhistory_cuttingtime_textview_text',
        );
        // 公制和英制保持一致
        value = cuttingMinutesOfImperial;
        break;
      case 2:
        title = Strings.getLang(
          'rn_61004_trackhistory_averagespeed_textview_text',
        );
        if (isImperial) {
          value = `${averageSpeedOfImperial} ${Strings.getLang(
            'rn_61004_trackhistory_mphunit_textview_text',
          )}`;
        } else {
          value = `${averageSpeedOfMetric} ${Strings.getLang(
            'rn_61004_trackhistory_kmperhunit_textview_text',
          )}`;
        }
        break;
      case 3:
        title = Strings.getLang('rn_61004_trackhistory_maxspeed_textview_text');
        if (isImperial) {
          value = `${maxSpeedOfImperial} ${Strings.getLang(
            'rn_61004_trackhistory_mphunit_textview_text',
          )}`;
        } else {
          value = `${maxSpeedOfOfMetric} ${Strings.getLang(
            'rn_61004_trackhistory_kmperhunit_textview_text',
          )}`;
        }
        break;
      case 4:
        title = Strings.getLang(
          'rn_61004_trackhistory_drivingdistance_textview_text',
        );
        if (isImperial) {
          value = `${drivingDistanceOfImperial} ${Strings.getLang(
            'rn_61004_trackhistory_milesunit_textview_text',
          )}`;
        } else {
          value = `${drivingDistanceOfMetric} ${Strings.getLang(
            'rn_61004_trackhistory_kmunit_textview_text',
          )}`;
        }
        break;
      default:
        break;
    }
    return {
      title,
      value,
    };
  });
  return result;
};

/**
 * 原始字符串(hexString或’0.0,0.0‘) ==> {latitude, longitude}
 * @param {String} gpsRawStr 原始字符串
 * @returns {latitude, longitude}
 */
export const formatCoordinate = (gpsRawStr = '') => {
  // 判断是否是hexString，如果是就解析
  let gpsStr = gpsRawStr;
  if (isHexString(gpsRawStr)) {
    gpsStr = StringUtils.hexToString(gpsRawStr) ?? '';
  }
  const newGpsStr = gpsStr.replace(/\\s/g, '');
  const [longitude, latitude] = newGpsStr.split(',');
  return [longitude, latitude];
};

export const getDefaultTimeStamp = () => {
  const nextQuarterIndex = getNextQuarterIndexFromTimeStamp();
  const currentHour = Math.trunc(nextQuarterIndex / 4);
  const currentMinute = nextQuarterIndex % 4;

  const date = new Date();
  date.setHours(currentHour);
  date.setMinutes(currentMinute * 15);
  date.setSeconds(0);

  const result = Math.floor(date.getTime() / 1000);
  return result;
};
/**
 * 跳转附件列表统一封装
 */
export const jumpToParts = (panel: {productId: string; deviceId: string}) => {
  const {deviceId, productId} = panel;
  const route = 'ChervonIot://EGO/DeviceManage/productFittings';
  const params = {
    deviceId,
    productId,
  };
  JumpUtils.jumpTo(route, params);
};
// 定位权限检测，申请
export const checkPermission = () => {
  PermissionUtils.titleMap = {
    UNAVAILABLE: Strings.getLang(
      'rn_common_permission_unavailable_textview_text',
    ),
    LIMITED: Strings.getLang('rn_common_permission_limited_textview_text'),
    BLOCKED: Strings.getLang('rn_common_permission_blocked_textview_text'),
  };
  PermissionUtils.checkAndRequestLocationPermisson();
  PermissionUtils.addAppStateChangeListener();
};
