export interface ChartDataProps {
  labels: string[]; // x轴数据
  data: number[]; // y轴数据
  unit: string; // 单位
  totalValue: number; // 总数
}

export interface EntryProps {
  deviceId: string;
  dateType: number;
  dateValue: string;
  totalValue: {[key: string]: number};
  dataList: {
    [key: string]: {value: string; key: string; empty: boolean}[];
  };
  lastSyncedTime: string;
}

export interface TotalValueProps {
  CO2Reduction?: string;
  mowingTime?: string;
  cuttingArea?: string;
  powerConsumption?: string;
  drivingDistance?: string;
}
