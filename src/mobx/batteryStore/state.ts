import {StringMap} from '../../types';

// 纯数据状态
export default class BatteryManagementState {
  /**
   * 电池电量数组
   */
  deviceBatteryLevelList: number[] = [];

  /**
   * 电池状态数组
   */
  deviceBatteryStatusList: number[] = [];

  /**
   * 电池仓位页面-DCDC电池充电剩余时间
   */
  DCBatteryChargeRemainTime: number = 0;

  /**
   * 电池仓位页面-DCDC电池是否充电
   */
  DCBatteryCharging: boolean = false;

  /** 电池百分比 */
  batteryPercentage: number = 0;

  /** 电池详情 */
  dcDetail: StringMap = {};
  /** 总电池详情 */
  totalBatteryDetail: StringMap = {};
}
