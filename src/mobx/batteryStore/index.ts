/**
 * 聚合模块
 * 电池管理
 * **功能范围**：电池仓位数据、电量状态、充电状态等。
 * */

import {makeAutoObservable} from 'mobx';
import BatteryState from './state';
import BatteryActions from './actions';
import {RootStore} from '../rootStore';
import {BaseStore} from '../base/BaseStore';

// 导出默认的BatteryManagementStore类，继承自BaseStore
export default class BatteryManagementStore extends BaseStore {
  // 声明actions属性，类型为BatteryActions
  actions: BatteryActions;
  // 声明state属性，类型为BatteryState
  state: BatteryState;
  // 构造函数，接收一个rootStore参数
  constructor(rootStore: RootStore) {
    // 调用父类的构造函数
    super(rootStore);
    // 初始化state属性
    this.state = new BatteryState();
    // 初始化actions属性
    this.actions = new BatteryActions(this.state, rootStore);
    // 使用makeAutoObservable函数，将state和actions中的属性和方法转换为可观察的
    makeAutoObservable(this);
  }

  /**
   * 重置组件状态
   */
  reset = () => {
    // 将state属性重置为新的BatteryState对象
    this.state = new BatteryState();
  };
}
