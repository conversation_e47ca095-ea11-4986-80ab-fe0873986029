import {makeAutoObservable} from 'mobx';
import DeviceConnectionState from './state';
import DeviceConnectionActions from './actions';
import {RootStore} from '../rootStore';
import {BaseStore} from '../base/BaseStore';

/**
 * 聚合模块
 * 设备状态与连接
 * **功能范围**：连接状态（BLE/WIFI）、设备工作状态、实时命令响应等。
 */
export default class DeviceConnectionStore extends BaseStore {
  state: DeviceConnectionState;
  actions: DeviceConnectionActions;
  constructor(rootStore: RootStore) {
    super(rootStore);
    this.state = new DeviceConnectionState();
    this.actions = new DeviceConnectionActions(this.state, rootStore);
    makeAutoObservable(this);
  }

  /**
   * 重置设备连接状态
   */
  reset = () => {
    this.state = new DeviceConnectionState();
  };
}
