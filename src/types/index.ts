/**
 * 类型定义统一导出文件
 */

// 设备相关类型
export * from './device';

// 电池相关类型
export * from './battery';

// API相关类型
export * from './api';

// 蓝牙相关类型
export * from './bluetooth';

// 通用类型
export * from './common';

// 数据解析常量
export const DATA_PARSING_CONSTANTS = {
  // 蓝牙数据解析
  BLUETOOTH: {
    WIFI_CMD_START_INDEX: 12,
    WIFI_CMD_END_OFFSET: -4,
    WIFI_DATA_LENGTH: 64,
    BATTERY_DATA_LENGTH: 16,
    BATTERY_DATA_HEADER_LENGTH: 4,
    HEX_GROUP_SIZE: 2,
    HEX_RADIX: 16,
  },

  // 日志相关
  LOGGING: {
    MAX_RAW_DATA_LOG_LENGTH: 100,
    MAX_ERROR_PREVIEW_LENGTH: 50,
  },

  // 数据验证
  VALIDATION: {
    MIN_ARRAY_LENGTH: 0,
    INITIAL_COUNT: 0,
  },
} as const;

// 正则表达式常量
export const REGEX_PATTERNS = {
  HEX_STRING: /^[0-9A-Fa-f]+$/,
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PHONE: /^1[3-9]\d{9}$/,
  MAC_ADDRESS: /^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/,
} as const;

// 时间相关常量
export const TIME_CONSTANTS = {
  MINUTES_PER_HOUR: 60,
  HOURS_PER_DAY: 24,
  DAYS_PER_WEEK: 7,
  MILLISECONDS_PER_SECOND: 1000,
  SECONDS_PER_MINUTE: 60,
  TIME_STRING_LENGTH: 2,
  TIME_PAD_CHAR: '0',
} as const;

// 应用配置常量
export const APP_CONSTANTS = {
  // 默认分页大小
  DEFAULT_PAGE_SIZE: 20,
  // 最大分页大小
  MAX_PAGE_SIZE: 100,
  // 默认超时时间 (毫秒)
  DEFAULT_TIMEOUT: 30000,
  // 重试次数
  DEFAULT_RETRY_COUNT: 3,
  // 缓存过期时间 (毫秒)
  DEFAULT_CACHE_DURATION: 300000, // 5分钟
} as const;
