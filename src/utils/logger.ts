/*
 * @Author: 邢立伟 <EMAIL>
 * @Date: 2024-09-11 11:00:00
 * @LastEditors: 邢立伟 <EMAIL>
 * @LastEditTime: 2024-09-11 11:00:00
 * @FilePath: /61004/src/utils/logger.ts
 * @Description: 日志工具，根据环境控制日志输出
 */

// 保存原始的console方法
/* eslint-disable no-console */
const originalConsole = {
  log: console.log,
  info: console.info,
  warn: console.warn,
  error: console.error,
  debug: console.debug,
  table: console.table,
};
/* eslint-enable no-console */

// 创建一个空函数作为生产环境的替代
const noop = () => {};

// 日志工具对象
const logger = {
  log: __DEV__ ? originalConsole.log : noop,
  info: __DEV__ ? originalConsole.info : noop,
  warn: originalConsole.warn, // 保留警告，即使在生产环境
  error: originalConsole.error, // 保留错误，即使在生产环境
  debug: __DEV__ ? originalConsole.debug : noop,
  table: __DEV__ ? originalConsole.table : noop,
};

// 在开发环境中，直接使用原始console
// 在生产环境中，替换console方法（除了warn和error）
if (!__DEV__) {
  /* eslint-disable no-console */
  console.log = noop;
  console.info = noop;
  console.debug = noop;
  console.table = noop;
  /* eslint-enable no-console */
}

export default logger;
