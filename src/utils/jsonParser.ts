/**
 * @description: 统一的JSON解析工具函数
 * @author: Senior Developer
 * @date: 2025-06-20
 *
 * 提供安全的JSON解析功能，包含错误处理、类型验证、默认值等功能
 * 符合高级开发最佳实践，确保应用稳定性
 */

import {logger} from './logger';

/**
 * JSON解析选项接口
 */
export interface JsonParseOptions<T = unknown> {
  /** 解析失败时的默认值 */
  defaultValue?: T;
  /** 是否在解析失败时抛出错误 */
  throwOnError?: boolean;
  /** 错误日志上下文信息 */
  context?: string;
  /** 类型验证函数 */
  validator?: (data: unknown) => data is T;
  /** 是否记录详细错误日志 */
  enableDetailedLogging?: boolean;
}

/**
 * JSON解析结果接口
 */
export interface JsonParseResult<T = unknown> {
  /** 解析是否成功 */
  success: boolean;
  /** 解析后的数据 */
  data: T;
  /** 错误信息（如果有） */
  error?: string;
  /** 原始错误对象 */
  originalError?: Error;
}

/**
 * 安全的JSON解析函数
 * @param jsonString - 要解析的JSON字符串
 * @param options - 解析选项
 * @returns 解析结果
 */
export function safeJsonParse<T = unknown>(
  jsonString: string | null | undefined,
  options: JsonParseOptions<T> = {},
): JsonParseResult<T> {
  const {
    defaultValue,
    throwOnError = false,
    context = 'Unknown',
    validator,
    enableDetailedLogging = true,
  } = options;

  // 输入验证
  const inputValidationResult = validateInput(
    jsonString,
    context,
    enableDetailedLogging,
    throwOnError,
    defaultValue as T,
  );
  if (!inputValidationResult.isValid) {
    return inputValidationResult.result;
  }

  try {
    const parsedData = JSON.parse(jsonString as string);

    // 类型验证
    if (validator && !validator(parsedData)) {
      return handleValidationError(
        context,
        enableDetailedLogging,
        throwOnError,
        defaultValue as T,
        parsedData,
      );
    }

    return {
      success: true,
      data: parsedData as T,
    };
  } catch (originalError) {
    return handleParsingError(
      originalError,
      context,
      enableDetailedLogging,
      throwOnError,
      defaultValue as T,
      jsonString as string,
    );
  }
}

/**
 * 验证输入参数
 */
function validateInput<T>(
  jsonString: string | null | undefined,
  context: string,
  enableDetailedLogging: boolean,
  throwOnError: boolean,
  defaultValue: T,
): {isValid: boolean; result?: JsonParseResult<T>} {
  if (jsonString === null || jsonString === undefined) {
    const error = 'Input is null or undefined';
    if (enableDetailedLogging) {
      logger.warn(`[JsonParser] ${context}: ${error}`);
    }

    if (throwOnError) {
      throw new Error(`JSON Parse Error in ${context}: ${error}`);
    }

    return {
      isValid: false,
      result: {
        success: false,
        data: defaultValue,
        error,
      },
    };
  }

  if (typeof jsonString !== 'string') {
    const error = `Input is not a string, received: ${typeof jsonString}`;
    if (enableDetailedLogging) {
      logger.warn(`[JsonParser] ${context}: ${error}`, {
        inputType: typeof jsonString,
        inputValue: jsonString,
      });
    }

    if (throwOnError) {
      throw new Error(`JSON Parse Error in ${context}: ${error}`);
    }

    return {
      isValid: false,
      result: {
        success: false,
        data: defaultValue,
        error,
      },
    };
  }

  // 空字符串处理
  if (jsonString.trim() === '') {
    const error = 'Input is empty string';
    if (enableDetailedLogging) {
      logger.warn(`[JsonParser] ${context}: ${error}`);
    }

    if (throwOnError) {
      throw new Error(`JSON Parse Error in ${context}: ${error}`);
    }

    return {
      isValid: false,
      result: {
        success: false,
        data: defaultValue,
        error,
      },
    };
  }

  return {isValid: true};
}

/**
 * 处理验证错误
 */
function handleValidationError<T>(
  context: string,
  enableDetailedLogging: boolean,
  throwOnError: boolean,
  defaultValue: T,
  parsedData: unknown,
): JsonParseResult<T> {
  const error = 'Parsed data failed validation';
  if (enableDetailedLogging) {
    logger.warn(`[JsonParser] ${context}: ${error}`, {
      parsedData,
      dataType: typeof parsedData,
    });
  }

  if (throwOnError) {
    throw new Error(`JSON Parse Error in ${context}: ${error}`);
  }

  return {
    success: false,
    data: defaultValue,
    error,
  };
}

/**
 * 处理解析错误
 */
function handleParsingError<T>(
  originalError: unknown,
  context: string,
  enableDetailedLogging: boolean,
  throwOnError: boolean,
  defaultValue: T,
  jsonString: string,
): JsonParseResult<T> {
  const error =
    originalError instanceof Error
      ? originalError.message
      : String(originalError);

  if (enableDetailedLogging) {
    logger.error(`[JsonParser] ${context}: JSON parsing failed`, {
      error,
      inputLength: jsonString.length,
      inputPreview: jsonString.substring(0, 100), // 只记录前100个字符
      stack: originalError instanceof Error ? originalError.stack : undefined,
    });
  }

  if (throwOnError) {
    throw new Error(`JSON Parse Error in ${context}: ${error}`);
  }

  return {
    success: false,
    data: defaultValue,
    error,
    originalError:
      originalError instanceof Error
        ? originalError
        : new Error(String(originalError)),
  };
}

/**
 * 简化版本的安全JSON解析，直接返回数据
 * @param jsonString - 要解析的JSON字符串
 * @param defaultValue - 默认值
 * @param context - 上下文信息
 * @returns 解析后的数据或默认值
 */
export function parseJsonSafely<T = unknown>(
  jsonString: string | null | undefined,
  defaultValue: T,
  context?: string,
): T {
  const result = safeJsonParse(jsonString, {
    defaultValue,
    context,
    throwOnError: false,
  });
  return result.data;
}

/**
 * 数组类型验证器
 */
export const isArray = (data: unknown): data is unknown[] =>
  Array.isArray(data);

/**
 * 对象类型验证器
 */
export const isObject = (data: unknown): data is Record<string, unknown> =>
  data !== null && typeof data === 'object' && !Array.isArray(data);

/**
 * 字符串类型验证器
 */
export const isString = (data: unknown): data is string =>
  typeof data === 'string';

/**
 * 数字类型验证器
 */
export const isNumber = (data: unknown): data is number =>
  typeof data === 'number' && !isNaN(data);

/**
 * 布尔类型验证器
 */
export const isBoolean = (data: unknown): data is boolean =>
  typeof data === 'boolean';

/**
 * 创建自定义验证器的工厂函数
 */
export function createValidator<T>(
  predicate: (data: unknown) => boolean,
  typeName: string,
): (data: unknown) => data is T {
  return (data: unknown): data is T => {
    const isValid = predicate(data);
    if (!isValid) {
      logger.warn(`[JsonParser] Validation failed: expected ${typeName}`, {
        actualType: typeof data,
        actualValue: data,
      });
    }
    return isValid;
  };
}

/**
 * 常用的复合验证器
 */
export const validators = {
  array: isArray,
  object: isObject,
  string: isString,
  number: isNumber,
  boolean: isBoolean,
  nonEmptyString: createValidator<string>(
    data => typeof data === 'string' && data.trim().length > 0,
    'non-empty string',
  ),
  nonEmptyArray: createValidator<unknown[]>(
    data => Array.isArray(data) && data.length > 0,
    'non-empty array',
  ),
  positiveNumber: createValidator<number>(
    data => typeof data === 'number' && !isNaN(data) && data > 0,
    'positive number',
  ),
};

export default {
  safeJsonParse,
  parseJsonSafely,
  validators,
  createValidator,
};
